# MediaCrawlerPro 数据库清理命令指南

## 📋 概述
本文档提供了通过直接数据库命令实现数据清理的方法，等效于执行 `python3 quick_clear_all_data.py --platform bilibili` 的效果。

## 🔧 数据库连接信息

### 服务器连接参数
```bash
# 数据库配置（基于config/db_config.py）
HOST: mysql_db (Docker内部) / **********:3307 (外部访问)
USER: root
PASSWORD: 123456
DATABASE: media_crawler
PORT: 3306 (内部) / 3307 (外部)
```

### 连接方式

#### 1. 通过Docker容器内部连接
```bash
# 进入MySQL容器
docker exec -it mysql_db mysql -u root -p123456 media_crawler

# 或者通过MediaCrawlerPro容器连接
docker exec -it mediacrawlerpro mysql -h mysql_db -u root -p123456 media_crawler
```

#### 2. 通过外部客户端连接
```bash
# 使用mysql客户端
mysql -h ********** -P 3307 -u root -p123456 media_crawler

# 使用mycli（如果安装）
mycli -h ********** -P 3307 -u root -p123456 media_crawler
```

## 📊 平台数据表结构

### Bilibili平台表
```sql
-- 视频信息表
bilibili_video

-- 视频评论表  
bilibili_video_comment

-- UP主信息表
bilibili_up_info
```

### 其他平台表
```sql
-- 小红书
xhs_note, xhs_note_comment, xhs_creator

-- 抖音
douyin_aweme, douyin_aweme_comment, dy_creator

-- 快手
kuaishou_video, kuaishou_video_comment, kuaishou_creator

-- 微博
weibo_note, weibo_note_comment, weibo_creator

-- 贴吧
tieba_note, tieba_comment, tieba_creator

-- 知乎
zhihu_content, zhihu_comment, zhihu_creator
```

## 🗑️ 数据清理命令

### 1. 清理Bilibili平台数据

#### 完整清理脚本（推荐）
```sql
-- 连接数据库
USE media_crawler;

-- 查看清理前的数据统计
SELECT 
    'bilibili_video_comment' as table_name, 
    COUNT(*) as record_count 
FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as table_name, 
    COUNT(*) as record_count 
FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as table_name, 
    COUNT(*) as record_count 
FROM bilibili_up_info;

-- 按顺序清理数据（先评论，再视频，最后UP主）
-- 1. 清理评论数据
DELETE FROM bilibili_video_comment;
SELECT ROW_COUNT() as '清理评论数量';

-- 2. 清理视频数据
DELETE FROM bilibili_video;
SELECT ROW_COUNT() as '清理视频数量';

-- 3. 清理UP主数据
DELETE FROM bilibili_up_info;
SELECT ROW_COUNT() as '清理UP主数量';

-- 验证清理结果
SELECT 
    'bilibili_video_comment' as table_name, 
    COUNT(*) as remaining_records 
FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as table_name, 
    COUNT(*) as remaining_records 
FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as table_name, 
    COUNT(*) as remaining_records 
FROM bilibili_up_info;
```

#### 单表清理命令
```sql
-- 只清理评论
DELETE FROM bilibili_video_comment;

-- 只清理视频
DELETE FROM bilibili_video;

-- 只清理UP主信息
DELETE FROM bilibili_up_info;
```

### 2. 清理所有平台数据

#### 完整清理脚本
```sql
-- 清理所有评论表
DELETE FROM bilibili_video_comment;
DELETE FROM xhs_note_comment;
DELETE FROM douyin_aweme_comment;
DELETE FROM kuaishou_video_comment;
DELETE FROM weibo_note_comment;
DELETE FROM tieba_comment;
DELETE FROM zhihu_comment;

-- 清理所有内容表
DELETE FROM bilibili_video;
DELETE FROM xhs_note;
DELETE FROM douyin_aweme;
DELETE FROM kuaishou_video;
DELETE FROM weibo_note;
DELETE FROM tieba_note;
DELETE FROM zhihu_content;

-- 清理所有创作者表
DELETE FROM bilibili_up_info;
DELETE FROM xhs_creator;
DELETE FROM dy_creator;
DELETE FROM kuaishou_creator;
DELETE FROM weibo_creator;
DELETE FROM tieba_creator;
DELETE FROM zhihu_creator;
```

### 3. 条件清理（高级用法）

#### 按时间范围清理
```sql
-- 清理指定日期之前的数据
DELETE FROM bilibili_video 
WHERE add_time < '2025-01-01 00:00:00';

-- 清理最近N天的数据
DELETE FROM bilibili_video_comment 
WHERE add_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);
```

#### 按特定条件清理
```sql
-- 清理特定UP主的数据
DELETE FROM bilibili_video 
WHERE user_id = '特定UP主ID';

-- 清理特定关键词的搜索结果
DELETE FROM bilibili_video 
WHERE title LIKE '%人工智能%';
```

## 📊 数据统计查询

### 查看数据库状态
```sql
-- 查看所有表的数据量
SELECT 
    TABLE_NAME as '表名',
    TABLE_ROWS as '估计行数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'media_crawler' 
    AND TABLE_NAME LIKE '%bilibili%'
ORDER BY TABLE_ROWS DESC;

-- 精确统计（较慢但准确）
SELECT 
    'bilibili_video' as table_name, 
    COUNT(*) as exact_count,
    MIN(add_time) as earliest_record,
    MAX(add_time) as latest_record
FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_video_comment' as table_name, 
    COUNT(*) as exact_count,
    MIN(add_time) as earliest_record,
    MAX(add_time) as latest_record
FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_up_info' as table_name, 
    COUNT(*) as exact_count,
    MIN(add_time) as earliest_record,
    MAX(add_time) as latest_record
FROM bilibili_up_info;
```

### 查看表结构
```sql
-- 查看表结构
DESCRIBE bilibili_video;
DESCRIBE bilibili_video_comment;
DESCRIBE bilibili_up_info;

-- 查看表的详细信息
SHOW CREATE TABLE bilibili_video;
```

## 🚀 快速操作脚本

### 一键清理Bilibili数据
```bash
#!/bin/bash
# 文件名: clear_bilibili.sh

echo "正在连接数据库并清理Bilibili数据..."

mysql -h ********** -P 3307 -u root -p123456 media_crawler << EOF
-- 显示清理前统计
SELECT 'BEFORE CLEANUP' as status;
SELECT 
    'bilibili_video_comment' as table_name, COUNT(*) as count FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as table_name, COUNT(*) as count FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as table_name, COUNT(*) as count FROM bilibili_up_info;

-- 执行清理
DELETE FROM bilibili_video_comment;
DELETE FROM bilibili_video;
DELETE FROM bilibili_up_info;

-- 显示清理后统计
SELECT 'AFTER CLEANUP' as status;
SELECT 
    'bilibili_video_comment' as table_name, COUNT(*) as count FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as table_name, COUNT(*) as count FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as table_name, COUNT(*) as count FROM bilibili_up_info;

SELECT 'CLEANUP COMPLETED' as status;
EOF

echo "Bilibili数据清理完成！"
```

### 使用方法
```bash
# 给脚本执行权限
chmod +x clear_bilibili.sh

# 执行清理
./clear_bilibili.sh
```

## ⚠️ 注意事项

### 1. 数据安全
- **清理操作不可逆**，执行前请确认
- 建议先在测试环境验证
- 重要数据请提前备份

### 2. 性能考虑
- 大量数据清理可能耗时较长
- 清理过程中数据库性能可能受影响
- 建议在业务低峰期执行

### 3. 事务处理
```sql
-- 使用事务确保数据一致性
START TRANSACTION;

DELETE FROM bilibili_video_comment;
DELETE FROM bilibili_video;
DELETE FROM bilibili_up_info;

-- 确认无误后提交
COMMIT;

-- 如有问题可回滚（仅在未提交前有效）
-- ROLLBACK;
```

### 4. 权限要求
- 需要对目标表的DELETE权限
- 建议使用具有足够权限的数据库用户

## 🔍 故障排查

### 常见错误及解决方案

#### 1. 连接失败
```bash
# 检查网络连接
telnet ********** 3307

# 检查Docker容器状态
docker ps | grep mysql
```

#### 2. 权限不足
```sql
-- 检查用户权限
SHOW GRANTS FOR 'root'@'%';

-- 授予权限（如需要）
GRANT DELETE ON media_crawler.* TO 'root'@'%';
FLUSH PRIVILEGES;
```

#### 3. 表不存在
```sql
-- 检查表是否存在
SHOW TABLES LIKE '%bilibili%';

-- 检查数据库
SHOW DATABASES;
USE media_crawler;
```

---

**版本**: v1.0  
**更新时间**: 2025-07-06  
**适用于**: MediaCrawlerPro数据库清理操作
./clear_bilibili_simple.sh --stats
./clear_bilibili_simple.sh --clear