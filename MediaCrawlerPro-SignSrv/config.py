# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。

# 主程序配置文件 - 整合所有配置模块
import os
from typing import List

# 导入所有配置模块的内容
try:
    from MediaCrawlerPro-Python.config.base_config import *
    from MediaCrawlerPro-Python.config.db_config import *
    from MediaCrawlerPro-Python.config.proxy_config import *
    from MediaCrawlerPro-Python.config.sign_srv_config import *
except ImportError:
    # 如果无法导入config包，则直接定义必要的配置
    from constant import MYSQL_ACCOUNT_SAVE

    # 基础配置
    PLATFORM = "bili"
    KEYWORDS = "deepseek,chatgpt"
    SORT_TYPE = "popularity_descending"
    PUBLISH_TIME_TYPE = 0
    CRAWLER_TYPE = "detail"
    SAVE_DATA_OPTION = "db"
    ACCOUNT_POOL_SAVE_TYPE = os.getenv("ACCOUNT_POOL_SAVE_TYPE", MYSQL_ACCOUNT_SAVE)
    START_PAGE = 1
    CRAWLER_MAX_NOTES_COUNT = 120
    MAX_CONCURRENCY_NUM = 3
    ENABLE_GET_COMMENTS = True
    ENABLE_GET_SUB_COMMENTS = True
    PER_NOTE_MAX_COMMENTS_COUNT = 0
    ENABLE_LOG_FILE = True

    # 批量爬取配置
    PROGRESS_FILE_PATH = "crawler_progress.json"
    BATCH_CRAWLER_LOG_FILE = "batch_crawler.log"
    ENABLE_BATCH_LOG = True
    MAX_RETRY_COUNT = 0
    RETRY_DELAY = 60
    ACCOUNT_PROCESS_DELAY_MIN = 2
    ACCOUNT_PROCESS_DELAY_MAX = 5
    VIDEO_PROCESS_DELAY_MIN = 0
    VIDEO_PROCESS_DELAY_MAX = 1

    # 冷却时间配置
    ENABLE_COOLDOWN = True
    COOLDOWN_TRIGGER_COUNT = 100
    COOLDOWN_DURATION = 60
    COOLDOWN_DISPLAY_INTERVAL = 10

    # 批量爬取文件路径配置
    BILI_UID_JSON = None
    BILI_BV_JSON = None
    BILI_BV_TXT = None
    BILI_BV_COMPLEX_JSON = None

    # 数据库配置
    MYSQL_DB_HOST = os.getenv("MYSQL_DB_HOST", "mysql_db")
    MYSQL_DB_PORT = int(os.getenv("MYSQL_DB_PORT", 3306))
    MYSQL_DB_PWD = os.getenv("MYSQL_DB_PWD", "123456")
    MYSQL_DB_USER = os.getenv("MYSQL_DB_USER", "root")
    MYSQL_DB_NAME = os.getenv("MYSQL_DB_NAME", "media_crawler")

    # 代理配置
    ENABLE_IP_PROXY = False
    IP_PROXY_POOL_COUNT = 10
    IP_PROXY_PROVIDER = "kuaidaili"

    # 签名服务配置
    SIGN_SRV_HOST = os.getenv("SIGN_SRV_HOST", "mediacrawler_signsrv")
    SIGN_SRV_PORT = int(os.getenv("SIGN_SRV_PORT", 8989))
