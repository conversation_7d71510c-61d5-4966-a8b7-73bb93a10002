# MediaCrawlerPro 离线部署包

## 📋 概述

这是MediaCrawlerPro的完整离线部署包，包含所有必需的Docker镜像和配置文件，可以在没有外部网络依赖的环境中部署运行。

## 📦 包含内容

```
MediaCrawlerPro-Offline-Deploy/
├── README.md                    # 本文档
├── docker-compose.yaml          # Docker Compose配置文件
├── load-images.sh              # 镜像加载脚本
├── deploy.sh                   # 部署脚本
├── schema/                     # 数据库表结构文件
│   ├── tables.sql              # 主表结构
│   └── *.sql                   # 其他DDL文件
├── mediacrawlerpro.tar         # 主应用Docker镜像
├── mysql.tar                   # MySQL 8.0 Docker镜像
├── redis.tar                   # Redis 7.0 Docker镜像
└── signsrv.tar                 # 签名服务Docker镜像
```

## 🖥️ 系统要求

### 硬件要求
- **CPU**: 2核心或以上
- **内存**: 4GB RAM或以上（推荐8GB）
- **存储**: 10GB可用空间或以上
- **网络**: 具有公网IP的服务器（用于API访问）

### 软件要求
- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **Docker**: 20.10.0+
- **Docker Compose**: 1.29.0+

## 🚀 Ubuntu云服务器部署指南

### 步骤1: 准备服务器环境

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Docker
sudo apt install -y docker.io

# 安装Docker Compose
sudo apt install -y docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组（可选，避免每次使用sudo）
sudo usermod -aG docker $USER
# 注意：添加用户组后需要重新登录才能生效
```

### 步骤2: 上传部署包

将整个 `MediaCrawlerPro-Offline-Deploy` 文件夹上传到服务器，例如：

```bash
# 使用scp上传（在本地执行）
scp -r MediaCrawlerPro-Offline-Deploy/ user@your-server-ip:/home/<USER>/

# 或使用rsync
rsync -avz MediaCrawlerPro-Offline-Deploy/ user@your-server-ip:/home/<USER>/MediaCrawlerPro-Offline-Deploy/
```

### 步骤3: 部署系统

```bash
# 进入部署目录
cd MediaCrawlerPro-Offline-Deploy

# 加载Docker镜像
./load-images.sh

# 部署系统
./deploy.sh
```

### 步骤4: 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 测试API接口
curl http://localhost:8080/health
```

## 🌐 端口配置

### 对外开放端口
- **8080**: HTTP API接口端口（必须对外开放）
  - 用途: 接收爬取任务请求
  - 访问: `http://服务器IP:8080`

### 可选开放端口
- **3307**: MySQL数据库端口（可选择性开放）
  - 用途: 外部数据库管理工具连接
  - 连接信息: 
    - 主机: 服务器IP
    - 端口: 3307
    - 用户名: root
    - 密码: 123456
    - 数据库: media_crawler

### 内部端口（无需开放）
- **6379**: Redis缓存（仅内部使用）
- **8989**: 签名服务（仅内部使用）
- **3306**: MySQL内部端口（映射到3307）

## 🔧 防火墙配置

### Ubuntu UFW配置
```bash
# 开放HTTP API端口
sudo ufw allow 8080/tcp

# 可选：开放MySQL端口（如需外部连接）
sudo ufw allow 3307/tcp

# 启用防火墙
sudo ufw enable
```

### CentOS/RHEL Firewalld配置
```bash
# 开放HTTP API端口
sudo firewall-cmd --permanent --add-port=8080/tcp

# 可选：开放MySQL端口
sudo firewall-cmd --permanent --add-port=3307/tcp

# 重载防火墙配置
sudo firewall-cmd --reload
```

## 📖 使用说明

### API接口使用

1. **健康检查**
```bash
curl http://服务器IP:8080/health
```

2. **创建爬取任务**
```bash
curl -X POST http://服务器IP:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "bv_numbers": ["BV1234567890", "BV0987654321"],
    "task_name": "测试任务"
  }'
```

3. **查询任务状态**
```bash
curl http://服务器IP:8080/api/tasks/{task_id}
```

### 数据库连接

如果开放了3307端口，可以使用数据库管理工具连接：
- **主机**: 服务器IP
- **端口**: 3307
- **用户名**: root
- **密码**: 123456
- **数据库**: media_crawler

## 🛠️ 管理命令

### 服务管理
```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f app

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新启动服务
docker-compose up -d
```

### 数据管理
```bash
# 备份数据库
docker exec mysql_db mysqldump -u root -p123456 media_crawler > backup.sql

# 清理系统数据
docker-compose exec app python quick_clear_all_data.py
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8080
# 或
sudo ss -tlnp | grep :8080
```

2. **服务启动失败**
```bash
# 查看详细日志
docker-compose logs app
docker-compose logs db
```

3. **数据库连接失败**
```bash
# 检查MySQL服务状态
docker-compose exec db mysql -u root -p123456 -e "SELECT 1"
```

4. **内存不足**
```bash
# 检查系统资源
free -h
df -h
docker system df
```

### 性能优化

1. **调整MySQL配置**（如需要）
2. **增加系统内存**
3. **使用SSD存储**
4. **调整Docker资源限制**

## 📞 技术支持

如遇到部署问题，请检查：
1. 系统资源是否充足
2. 防火墙配置是否正确
3. Docker服务是否正常运行
4. 端口是否被其他服务占用

## 📄 许可证

本项目遵循相应的开源许可证，详见源码仓库。
