# MediaCrawlerPro 快速部署指南

## 🚀 5分钟快速部署

### 前提条件
- Ubuntu 18.04+ 云服务器
- 具有sudo权限的用户
- 服务器具有公网IP

### 一键部署命令

```bash
# 1. 安装Docker环境
sudo apt update && sudo apt install -y docker.io docker-compose
sudo systemctl start docker && sudo systemctl enable docker

# 2. 上传部署包到服务器（在本地执行）
scp -r MediaCrawlerPro-Offline-Deploy/ user@your-server-ip:/home/<USER>/

# 3. 在服务器上执行部署
cd MediaCrawlerPro-Offline-Deploy
./load-images.sh && ./deploy.sh

# 4. 配置防火墙
sudo ufw allow 8080/tcp && sudo ufw --force enable
```

### 验证部署

```bash
# 检查服务状态
curl http://localhost:8080/health

# 如果返回 {"status": "ok"} 则部署成功
```

## 📋 核心信息速查

### 端口信息
- **8080**: HTTP API接口（必须开放）
- **3307**: MySQL数据库（可选开放）

### 默认配置
- **MySQL用户名**: root
- **MySQL密码**: 123456
- **数据库名**: media_crawler

### 管理命令
```bash
# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down
```

### API使用示例
```bash
# 创建爬取任务
curl -X POST http://服务器IP:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{"bv_numbers": ["BV1234567890"], "task_name": "测试"}'
```

## ⚠️ 注意事项

1. **确保服务器有足够资源**：至少4GB内存，10GB存储空间
2. **开放必要端口**：8080端口必须对外开放
3. **等待服务启动**：首次启动需要30-60秒初始化数据库
4. **检查防火墙**：确保云服务器安全组也开放了8080端口

## 🔧 故障排除

### 服务无法访问
```bash
# 检查服务状态
docker-compose ps

# 检查端口监听
sudo netstat -tlnp | grep 8080

# 检查防火墙
sudo ufw status
```

### 数据库连接失败
```bash
# 等待数据库初始化完成
docker-compose logs db | grep "ready for connections"

# 手动测试数据库连接
docker-compose exec db mysql -u root -p123456 -e "SELECT 1"
```

完整文档请参考 `README.md`
