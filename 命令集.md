# MediaCrawlerPro 命令集

./clear_bilibili_simple.sh --clear
## 目录
- [Docker 镜像构建与部署](#docker-镜像构建与部署)
- [服务器连接与文件传输](#服务器连接与文件传输)
- [容器管理](#容器管理)
- [数据库操作](#数据库操作)
- [API 测试](#api-测试)
- [日志查看](#日志查看)
- [常见错误与解决方案](#常见错误与解决方案)

## Docker 镜像构建与部署

### 1. 构建跨平台镜像（Mac -> Linux x86）
```bash
# 在 MediaCrawlerPro-Python 目录下执行
docker build --platform linux/amd64 -t mediacrawlerpro:x86-v7-fixed .

# ⚠️ 注意事项：
# - 必须在包含 Dockerfile 的目录下执行
# - --platform linux/amd64 是关键，确保在 Mac 上构建 x86 镜像
# - 标签命名建议包含版本号和架构信息
```

### 2. 保存镜像为文件
```bash
# 压缩保存（推荐，文件较小）
docker save mediacrawlerpro:x86-v7-fixed | gzip > mediacrawlerpro-x86-v7-fixed.tar.gz

# 未压缩保存（文件较大，但传输更稳定）
docker save mediacrawlerpro:x86-v7-fixed > mediacrawlerpro-x86-v7-fixed.tar

# ⚠️ 注意事项：
# - 压缩文件约 1.3GB，未压缩约 2.9GB
# - 网络不稳定时建议使用未压缩版本
```

## 服务器连接与文件传输

### 1. SSH 连接
```bash
# 使用密钥连接
ssh -i /Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem ubuntu@**********

# ⚠️ 注意事项：
# - 密钥文件权限必须是 600: chmod 600 miyao.pem
# - 用户名是 ubuntu，不是 root
```

### 2. 文件上传
```bash
# 上传到用户目录（推荐）
scp -i /Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem mediacrawlerpro-x86-v7-fixed.tar.gz ubuntu@**********:~/

# ⚠️ 注意事项：
# - 不要直接上传到 /root 目录，会有权限问题
# - 大文件上传可能需要较长时间，建议使用稳定网络
# - 上传完成后需要移动到目标目录
```

### 3. 移动文件到目标目录
```bash
# 移动到独立存储目录
sudo mv ~/mediacrawlerpro-x86-v7-fixed.tar.gz /root/data/disk/MediaCrawlerPro/
```

## 容器管理

### 1. 停止和删除旧容器
```bash
# 停止容器
sudo docker stop mediacrawlerpro

# 删除容器
sudo docker rm mediacrawlerpro

# 一键停止并删除
sudo docker stop mediacrawlerpro && sudo docker rm mediacrawlerpro

# ⚠️ 注意事项：
# - 必须先停止再删除
# - 容器名称必须准确匹配
```

### 2. 加载镜像
```bash
# 从压缩文件加载
sudo gunzip -c /root/data/disk/MediaCrawlerPro/mediacrawlerpro-x86-v7-fixed.tar.gz | sudo docker load

# 从未压缩文件加载
sudo docker load < /root/data/disk/MediaCrawlerPro/mediacrawlerpro-x86-v7-fixed.tar

# ⚠️ 注意事项：
# - 压缩文件使用 gunzip -c 管道加载
# - 加载过程可能需要几分钟
# - 确保文件路径正确
```

### 3. 启动容器
```bash
# 标准启动命令（关键！）
sudo docker run -d --name mediacrawlerpro --network mediacrawlerpro-final-deploy_default -p 8080:8080 mediacrawlerpro:x86-v7-fixed

# ⚠️ 重要注意事项：
# - --network mediacrawlerpro-final-deploy_default 是关键，必须使用正确的网络
# - 端口映射 -p 8080:8080 确保外部访问
# - -d 后台运行
# - --name 指定容器名称，便于管理
```

### 4. 查看容器状态
```bash
# 查看所有容器
sudo docker ps -a

# 查看特定容器
sudo docker ps -a | grep mediacrawlerpro

# 查看容器详细信息
sudo docker inspect mediacrawlerpro
```

## 数据库操作

### 1. 连接数据库
```bash
# 进入 MySQL 容器
sudo docker exec -it mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -p

# ⚠️ 注意事项：
# - 容器名称可能因部署方式而异
# - 密码通常在 docker-compose.yml 中定义
```

### 2. 数据库清空脚本
```bash
# 使用 SSH 密钥连接并清空数据
ssh -i /Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem ubuntu@********** << 'EOF'
sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler << 'SQL'
-- 清空 bilibili 平台数据
DELETE FROM bilibili_video WHERE 1=1;
DELETE FROM bilibili_video_comment WHERE 1=1;
DELETE FROM bilibili_creator WHERE 1=1;
-- 重置自增ID
ALTER TABLE bilibili_video AUTO_INCREMENT = 1;
ALTER TABLE bilibili_video_comment AUTO_INCREMENT = 1;
ALTER TABLE bilibili_creator AUTO_INCREMENT = 1;
-- 显示清空结果
SELECT 'bilibili_video' as table_name, COUNT(*) as count FROM bilibili_video
UNION ALL
SELECT 'bilibili_video_comment', COUNT(*) FROM bilibili_video_comment
UNION ALL
SELECT 'bilibili_creator', COUNT(*) FROM bilibili_creator;
SQL
EOF

# ⚠️ 注意事项：
# - 密码是 media_crawler_2024
# - 数据库名是 media_crawler
# - 使用 << 'EOF' 避免变量替换问题
```

### 3. 数据统计查询
```bash
# 查看数据统计
ssh -i /Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem ubuntu@********** << 'EOF'
sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler << 'SQL'
SELECT
    'bilibili_video' as table_name,
    COUNT(*) as count,
    MAX(created_time) as latest_created
FROM bilibili_video
UNION ALL
SELECT
    'bilibili_video_comment',
    COUNT(*),
    MAX(created_time)
FROM bilibili_video_comment;
SQL
EOF
```

## API 测试

### 1. 健康检查
```bash
# 本地测试
curl http://localhost:8080/health

# 服务器测试
curl http://**********:8080/health

# ⚠️ 注意事项：
# - 确保端口 8080 已开放
# - 返回 JSON 格式表示服务正常
```

### 2. BV 号爬取测试
```bash
# 基础 BV 爬取
curl -X POST http://**********:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"],
    "enable_comments": true,
    "enable_sub_comments": true,
    "max_comments_per_video": 100
  }'

# ⚠️ 注意事项：
# - 使用 bv_list 数组格式
# - enable_comments 控制是否爬取评论
# - max_comments_per_video 限制评论数量
```

### 3. 关键词搜索爬取
```bash
# 关键词搜索
curl -X POST http://**********:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "search",
    "keywords": "人工智能",
    "max_videos_per_keyword": 10,
    "enable_comments": true,
    "max_comments_per_video": 50
  }'
```

### 4. 可用的 type 参数
```bash
# 查看支持的爬取类型
curl http://**********:8080/tasks/types

# 支持的类型：
# - detail: BV号详情爬取
# - search: 关键词搜索爬取
# - creator: 创作者信息爬取
```

## 日志查看

### 1. 容器日志
```bash
# 查看最新 50 行日志
sudo docker logs mediacrawlerpro --tail 50

# 实时跟踪日志
sudo docker logs -f mediacrawlerpro

# 查看特定时间段日志
sudo docker logs mediacrawlerpro --since="2025-07-06T13:30:00"

# ⚠️ 注意事项：
# - 日志量可能很大，建议使用 --tail 限制行数
# - -f 参数用于实时跟踪，Ctrl+C 退出
```

### 2. 过滤特定日志
```bash
# 查看错误日志
sudo docker logs mediacrawlerpro 2>&1 | grep -i error

# 查看账号相关日志
sudo docker logs mediacrawlerpro 2>&1 | grep -E "(账号|account|Account)"

# 查看代理相关日志
sudo docker logs mediacrawlerpro 2>&1 | grep -E "(proxy|代理)"

# 查看爬取进度日志
sudo docker logs mediacrawlerpro 2>&1 | grep -E "(处理进度|成功|失败)"
```

### 3. 停止当前爬取任务
```bash
# 重启容器停止爬取
sudo docker restart mediacrawlerpro

# 查看停止后的状态
sudo docker logs mediacrawlerpro --tail 20
```

## 常见错误与解决方案

### 1. 网络连接错误
```bash
# 错误：容器无法连接数据库
# 解决：检查网络配置
sudo docker network ls
sudo docker network inspect mediacrawlerpro-final-deploy_default

# 确保容器在正确的网络中
sudo docker run -d --name mediacrawlerpro --network mediacrawlerpro-final-deploy_default -p 8080:8080 mediacrawlerpro:x86-v7-fixed
```

### 2. 权限错误
```bash
# 错误：Permission denied
# 解决：使用 sudo 或检查文件权限
sudo chmod 600 /Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem
sudo chown ubuntu:ubuntu /path/to/file
```

### 3. 端口占用
```bash
# 检查端口占用
sudo netstat -tulpn | grep 8080
sudo lsof -i :8080

# 停止占用端口的进程
sudo kill -9 <PID>
```

### 4. 镜像加载失败
```bash
# 错误：unexpected end of file
# 原因：文件传输不完整
# 解决：重新上传文件，检查文件大小
ls -la mediacrawlerpro-x86-v7-fixed.tar.gz
# 本地文件应该约 1.3GB，服务器文件大小应该一致
```

### 5. 账号池问题
```bash
# 错误：账号池中没有可用的账号
# 解决：检查数据库中的账号配置
sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler -e "SELECT * FROM account_info WHERE platform_name='bili';"
```

### 6. 代理配置问题
```bash
# 错误：代理连接失败
# 解决：检查代理配置和凭据
# 在 config.py 中确认：
# ENABLE_IP_PROXY = True
# KDL_SECERT_ID, KDL_SIGNATURE, KDL_USER_NAME, KDL_USER_PWD 正确配置
```

## 版本管理

### 1. 清理旧版本
```bash
# 查看所有镜像
sudo docker images | grep mediacrawlerpro

# 删除旧镜像
sudo docker rmi mediacrawlerpro:old-version

# 清理未使用的镜像
sudo docker image prune -f

# 批量删除旧版本（保留最新版本）
sudo docker images | grep mediacrawlerpro | grep -v x86-v7-fixed | awk '{print $3}' | xargs sudo docker rmi
```

### 2. 备份重要数据
```bash
# 备份数据库
sudo docker exec mediacrawlerpro-final-deploy_mysql_db_1 mysqldump -u root -pmedia_crawler_2024 media_crawler > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份配置文件
cp -r MediaCrawlerPro-Python/config/ backup_config_$(date +%Y%m%d_%H%M%S)/

# 备份镜像
docker save mediacrawlerpro:x86-v7-fixed | gzip > backup_mediacrawlerpro_$(date +%Y%m%d_%H%M%S).tar.gz
```

## 监控与维护

### 1. 系统资源监控
```bash
# 查看容器资源使用
sudo docker stats mediacrawlerpro

# 查看磁盘使用
df -h /root/data/disk/

# 查看内存使用
free -h

# 查看 Docker 系统信息
sudo docker system df
```

### 2. 定期维护任务
```bash
# 清理 Docker 系统
sudo docker system prune -f

# 清理未使用的镜像
sudo docker image prune -f

# 清理未使用的容器
sudo docker container prune -f

# 清理未使用的网络
sudo docker network prune -f
```

## 配置文件管理

### 1. 关键配置文件位置
```bash
# 主配置文件
MediaCrawlerPro-Python/config.py

# 重要配置项：
# - ENABLE_IP_PROXY = True  # 启用代理
# - IP_PROXY_POOL_COUNT = 2  # 代理池数量
# - IP_PROXY_PROVIDER_NAME = "kuaidaili"  # 代理提供商
# - 快代理凭据：KDL_SECERT_ID, KDL_SIGNATURE, KDL_USER_NAME, KDL_USER_PWD
```

### 2. 配置文件检查
```bash
# 检查配置文件语法
python3 -c "import config; print('配置文件语法正确')"

# 检查代理配置
python3 -c "import config; print(f'代理启用: {config.ENABLE_IP_PROXY}')"
```

## 故障排除流程

### 1. 服务无法启动
```bash
# 1. 检查容器状态
sudo docker ps -a | grep mediacrawlerpro

# 2. 查看启动日志
sudo docker logs mediacrawlerpro

# 3. 检查网络连接
sudo docker network inspect mediacrawlerpro-final-deploy_default

# 4. 检查端口占用
sudo netstat -tulpn | grep 8080
```

### 2. 爬取失败
```bash
# 1. 查看爬取日志
sudo docker logs mediacrawlerpro --tail 100 | grep -E "(失败|error|Error)"

# 2. 检查账号状态
sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler -e "SELECT * FROM account_info WHERE platform_name='bili';"

# 3. 检查代理状态
sudo docker logs mediacrawlerpro --tail 100 | grep -E "(proxy|代理)"

# 4. 重启服务
sudo docker restart mediacrawlerpro
```

---

## 快速部署检查清单

### 构建阶段
- [ ] 1. 在正确目录执行构建：`cd MediaCrawlerPro-Python`
- [ ] 2. 构建跨平台镜像：`docker build --platform linux/amd64 -t mediacrawlerpro:x86-v7-fixed .`
- [ ] 3. 保存镜像：`docker save mediacrawlerpro:x86-v7-fixed | gzip > mediacrawlerpro-x86-v7-fixed.tar.gz`
- [ ] 4. 检查文件大小：`ls -la mediacrawlerpro-x86-v7-fixed.tar.gz` (应约1.3GB)

### 部署阶段
- [ ] 5. 上传到服务器：`scp -i miyao.pem mediacrawlerpro-x86-v7-fixed.tar.gz ubuntu@**********:~/`
- [ ] 6. 移动到目标目录：`sudo mv ~/mediacrawlerpro-x86-v7-fixed.tar.gz /root/data/disk/MediaCrawlerPro/`
- [ ] 7. 停止旧容器：`sudo docker stop mediacrawlerpro && sudo docker rm mediacrawlerpro`
- [ ] 8. 加载新镜像：`sudo gunzip -c /root/data/disk/MediaCrawlerPro/mediacrawlerpro-x86-v7-fixed.tar.gz | sudo docker load`
- [ ] 9. 启动容器：`sudo docker run -d --name mediacrawlerpro --network mediacrawlerpro-final-deploy_default -p 8080:8080 mediacrawlerpro:x86-v7-fixed`

### 验证阶段
- [ ] 10. 检查容器状态：`sudo docker ps -a | grep mediacrawlerpro`
- [ ] 11. 测试健康检查：`curl http://**********:8080/health`
- [ ] 12. 查看启动日志：`sudo docker logs mediacrawlerpro --tail 50`
- [ ] 13. 测试 API 功能：发送 BV 爬取请求
- [ ] 14. 验证数据存储：查询数据库统计

### 关键成功因素
- ✅ **正确的 Docker 网络配置**：`--network mediacrawlerpro-final-deploy_default`
- ✅ **完整的文件传输**：确保上传文件大小正确
- ✅ **准确的容器启动参数**：网络、端口、名称配置
- ✅ **有效的代理和账号配置**：确保 config.py 中代理设置正确
- ✅ **数据库连接正常**：容器能正常连接到 MySQL 数据库

### 常用维护命令
```bash
# 查看服务状态
curl http://**********:8080/health

# 查看实时日志
sudo docker logs -f mediacrawlerpro

# 重启服务
sudo docker restart mediacrawlerpro

# 清空数据库
ssh -i miyao.pem ubuntu@********** "sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler -e 'DELETE FROM bilibili_video; DELETE FROM bilibili_video_comment;'"

# 查看数据统计
ssh -i miyao.pem ubuntu@********** "sudo docker exec -i mediacrawlerpro-final-deploy_mysql_db_1 mysql -u root -pmedia_crawler_2024 media_crawler -e 'SELECT COUNT(*) as video_count FROM bilibili_video; SELECT COUNT(*) as comment_count FROM bilibili_video_comment;'"
```

---

**最后更新时间**: 2025-07-06
**当前版本**: mediacrawlerpro:x86-v7-fixed
**服务器地址**: http://**********:8080