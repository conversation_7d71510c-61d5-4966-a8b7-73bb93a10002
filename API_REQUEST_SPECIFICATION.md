# MediaCrawlerPro API 请求规范

## 📋 目录
- [基础信息](#基础信息)
- [认证方式](#认证方式)
- [API端点](#api端点)
- [请求格式](#请求格式)
- [响应格式](#响应格式)
- [错误处理](#错误处理)
- [使用示例](#使用示例)

## 🌐 基础信息

### 服务地址
- **本地开发**: `http://localhost:8080`
- **生产环境**: `http://**********:8080`

### 通用请求头
```http
Content-Type: application/json
Accept: application/json
```

### 支持的HTTP方法
- `GET` - 查询操作
- `POST` - 创建任务
- `DELETE` - 取消任务

## 🔐 认证方式
当前版本无需认证，直接访问即可。

## 📡 API端点

### 1. 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "service": "MediaCrawlerPro HTTP Middleware",
  "status": "ok",
  "timestamp": "2025-07-05T18:01:31.801217"
}
```

### 2. 创建爬取任务
```http
POST /tasks
```

### 3. 查询任务状态
```http
GET /tasks/{task_id}
```

### 4. 查询所有任务
```http
GET /tasks
```

### 5. 取消任务
```http
DELETE /tasks/{task_id}
```

## 📝 请求格式

### 任务类型概览

| 类型 | 说明 | 适用场景 |
|------|------|----------|
| `detail` | 详细信息爬取 | 有具体BV号列表 |
| `search` | 关键词搜索 | 根据关键词发现内容 |
| `creator` | UP主数据爬取 | 获取特定UP主所有视频 |
| `homefeed` | 首页推荐 | 获取热门推荐内容 |

### 🎛️ 可配置爬取参数

所有任务类型都支持以下可选参数来控制爬取行为：

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `max_videos` | `int` | `120` | 爬取视频的最大数量 |
| `enable_comments` | `bool` | `true` | 是否爬取视频评论 |
| `enable_sub_comments` | `bool` | `true` | 是否爬取二级评论(回复) |
| `max_comments_per_video` | `int` | `0` | 每个视频的最大评论数量，0表示不限制 |

**参数使用示例**:
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "人工智能",
  "max_videos": 50,
  "enable_comments": true,
  "enable_sub_comments": false,
  "max_comments_per_video": 100
}
```

### 1. 详细信息爬取 (detail)

#### 方式1: BV号数组 (推荐)
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg", "BV1zvK9zSEvD"]
}
```

**带爬取参数的BV号爬取**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"],
  "enable_comments": true,
  "enable_sub_comments": true,
  "max_comments_per_video": 200
}
```

#### 方式2: 复杂JSON数据
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_data": [
    {
      "code": 0,
      "data": {
        "has_more": false,
        "items": [
          {
            "fields": {
              "bv": [
                {
                  "text": "BV1aRKHz2Ejd",
                  "type": "text"
                }
              ]
            },
            "record_id": "rec25lYbdy6iNR"
          }
        ],
        "total": 1
      },
      "msg": "success"
    }
  ]
}
```

#### 方式3: 文件路径
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_json_file": "/path/to/bv.json"
}
```

### 2. 关键词搜索 (search)
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "人工智能"
}
```

**多关键词搜索**:
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "人工智能,机器学习,深度学习"
}
```

**带爬取参数的搜索**:
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "人工智能",
  "max_videos": 50,
  "enable_comments": true,
  "enable_sub_comments": false,
  "max_comments_per_video": 100
}
```

### 3. UP主数据爬取 (creator)
```json
{
  "platform": "bili",
  "type": "creator",
  "uid_json_file": "/path/to/uid_list.json"
}
```

### 4. 首页推荐 (homefeed)
```json
{
  "platform": "bili",
  "type": "homefeed"
}
```

## 📤 响应格式

### 创建任务响应
```json
{
  "task_id": "0e39a2c6-1435-47de-9f24-9ac4e93a79dc",
  "status": "created",
  "message": "任务已创建并开始执行"
}
```

### 任务状态响应
```json
{
  "id": "0e39a2c6-1435-47de-9f24-9ac4e93a79dc",
  "status": "running",
  "config": {
    "platform": "bili",
    "type": "search",
    "keywords": "人工智能"
  },
  "created_at": "2025-07-05T18:01:31.801217",
  "started_at": "2025-07-05T18:01:31.801903",
  "completed_at": null,
  "error": null,
  "progress": {
    "total": 0,
    "processed": 0,
    "failed": 0,
    "success": 0
  }
}
```

### 任务状态说明
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 执行完成
- `failed`: 执行失败
- `cancelled`: 已取消

### 任务列表响应
```json
{
  "tasks": [
    {
      "id": "task_id_1",
      "status": "completed",
      "config": {...},
      "created_at": "2025-07-05T18:01:31"
    },
    {
      "id": "task_id_2", 
      "status": "running",
      "config": {...},
      "created_at": "2025-07-05T18:02:15"
    }
  ],
  "total": 2
}
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "error": "错误描述信息",
  "code": "ERROR_CODE",
  "details": "详细错误信息"
}
```

### 常见错误码
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 任务不存在
- `500 Internal Server Error`: 服务器内部错误

### 错误示例
```json
{
  "error": "必须指定BV号数组、复杂JSON数据、文件路径或关键词"
}
```

## 💡 使用示例

### cURL 示例

#### 1. 健康检查
```bash
curl -X GET http://**********:8080/health
```

#### 2. 创建BV详细信息任务
```bash
curl -X POST http://**********:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"]
  }'
```

#### 3. 创建关键词搜索任务
```bash
curl -X POST http://**********:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "search",
    "keywords": "人工智能"
  }'
```

#### 4. 查询任务状态
```bash
curl -X GET http://**********:8080/tasks/TASK_ID
```

#### 5. 查询所有任务
```bash
curl -X GET http://**********:8080/tasks
```

#### 6. 取消任务
```bash
curl -X DELETE http://**********:8080/tasks/TASK_ID
```

### Python 示例

```python
import requests
import json

# 基础配置
BASE_URL = "http://**********:8080"
HEADERS = {"Content-Type": "application/json"}

# 1. 健康检查
def health_check():
    response = requests.get(f"{BASE_URL}/health")
    return response.json()

# 2. 创建BV详细信息任务
def create_detail_task(bv_list):
    data = {
        "platform": "bili",
        "type": "detail",
        "bv_list": bv_list
    }
    response = requests.post(f"{BASE_URL}/tasks", 
                           headers=HEADERS, 
                           data=json.dumps(data))
    return response.json()

# 3. 创建关键词搜索任务
def create_search_task(keywords):
    data = {
        "platform": "bili",
        "type": "search", 
        "keywords": keywords
    }
    response = requests.post(f"{BASE_URL}/tasks",
                           headers=HEADERS,
                           data=json.dumps(data))
    return response.json()

# 4. 查询任务状态
def get_task_status(task_id):
    response = requests.get(f"{BASE_URL}/tasks/{task_id}")
    return response.json()

# 5. 查询所有任务
def get_all_tasks():
    response = requests.get(f"{BASE_URL}/tasks")
    return response.json()

# 6. 取消任务
def cancel_task(task_id):
    response = requests.delete(f"{BASE_URL}/tasks/{task_id}")
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 健康检查
    health = health_check()
    print("健康状态:", health)
    
    # 创建搜索任务
    task = create_search_task("人工智能")
    task_id = task.get("task_id")
    print("任务创建:", task)
    
    # 查询任务状态
    status = get_task_status(task_id)
    print("任务状态:", status)
```

## 📚 最佳实践

### 1. 任务创建
- 优先使用 `bv_list` 方式传递BV号，简洁高效
- 关键词搜索使用逗号分隔多个关键词
- 文件路径必须是服务器可访问的绝对路径

### 2. 状态监控
- 定期查询任务状态，建议间隔10-30秒
- 任务完成后及时获取结果
- 长时间运行的任务可考虑设置超时

### 3. 错误处理
- 始终检查HTTP状态码
- 解析错误响应中的详细信息
- 实现重试机制处理临时性错误

### 4. 性能优化
- 批量处理BV号而非单个请求
- 合理控制并发任务数量
- 避免频繁创建相同类型的任务

## 🔧 高级配置

### 文件格式支持

#### BV号文件格式

**简单JSON格式**:
```json
["BV1aRKHz2Ejd", "BV1ceK9zHEpg", "BV1zvK9zSEvD"]
```

**复杂JSON格式** (支持您的bv.json):
```json
[
  {
    "code": 0,
    "data": {
      "has_more": false,
      "items": [
        {
          "fields": {
            "bv": [
              {
                "text": "BV1aRKHz2Ejd",
                "type": "text"
              }
            ]
          },
          "record_id": "rec25lYbdy6iNR"
        }
      ],
      "total": 1
    },
    "msg": "success"
  }
]
```

**TXT格式**:
```
BV1aRKHz2Ejd
BV1ceK9zHEpg
BV1zvK9zSEvD
```

#### UID文件格式 (UP主ID)
```json
["123456789", "987654321", "456789123"]
```

### 请求限制
- **单次BV号数量**: 建议不超过100个
- **关键词长度**: 单个关键词不超过50字符
- **并发任务数**: 建议不超过5个
- **请求频率**: 建议间隔1秒以上

### 数据输出
爬取的数据将保存到MySQL数据库中，包含以下表：
- `bilibili_video`: 视频基本信息
- `bilibili_video_comment`: 视频评论
- `bilibili_creator`: UP主信息

## 🚨 注意事项

### 1. 合规使用
- 仅供学习和研究目的使用
- 遵守目标平台的使用条款和robots.txt规则
- 不得用于商业用途或大规模爬取
- 合理控制请求频率，避免对平台造成负担

### 2. 技术限制
- 服务器需要稳定的网络连接
- 某些视频可能因权限限制无法获取
- 评论数据获取可能受到平台反爬限制

### 3. 数据质量
- 搜索结果数量受平台API限制
- 历史数据可能存在时效性问题
- 部分字段可能为空或不完整

## 📞 技术支持

### 常见问题排查

**问题1**: 任务创建失败
- 检查请求格式是否正确
- 验证BV号格式是否有效
- 确认服务器状态是否正常

**问题2**: 任务长时间运行
- 大量数据爬取需要较长时间
- 网络状况可能影响执行速度
- 可通过任务状态API监控进度

**问题3**: 数据获取不完整
- 某些视频可能有访问限制
- 评论数据可能受到平台限制
- 检查错误日志获取详细信息

### 联系方式
- **项目地址**: MediaCrawlerPro
- **技术文档**: 参考项目README.md
- **问题反馈**: 通过GitHub Issues提交

---

**版本**: v1.0
**更新时间**: 2025-07-05
**维护者**: MediaCrawlerPro Team
**文档状态**: ✅ 已验证并测试通过
