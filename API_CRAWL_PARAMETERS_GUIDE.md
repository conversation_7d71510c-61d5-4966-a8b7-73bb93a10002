# MediaCrawlerPro API 爬取参数配置指南

## 📋 概述

MediaCrawlerPro HTTP API 现已支持动态配置爬取参数，您可以在API请求中指定以下参数来控制爬取行为：

## 🎛️ 支持的参数

### 1. 视频数量控制
- **参数名**: `max_videos`
- **类型**: `int`
- **默认值**: `120`
- **说明**: 控制爬取视频的最大数量
- **适用场景**: 关键词搜索、UP主爬取等

### 2. 评论爬取控制
- **参数名**: `enable_comments`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否爬取视频评论
- **适用场景**: 所有爬取类型

### 3. 二级评论控制
- **参数名**: `enable_sub_comments`
- **类型**: `bool`
- **默认值**: `true`
- **说明**: 是否爬取二级评论(评论的回复)
- **适用场景**: 所有爬取类型

### 4. 单视频评论数量限制
- **参数名**: `max_comments_per_video`
- **类型**: `int`
- **默认值**: `0` (不限制)
- **说明**: 每个视频爬取的最大评论数量，0表示不限制
- **适用场景**: 所有爬取类型

## 💡 使用示例

### 示例1: 关键词搜索 - 精简爬取
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "人工智能",
  "max_videos": 20,
  "enable_comments": false,
  "enable_sub_comments": false
}
```
**说明**: 只爬取20个视频的基本信息，不爬取评论

### 示例2: BV号详细爬取 - 限制评论数量
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"],
  "enable_comments": true,
  "enable_sub_comments": true,
  "max_comments_per_video": 100
}
```
**说明**: 爬取指定BV号的详细信息，每个视频最多爬取100条评论

### 示例3: 关键词搜索 - 完整爬取
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "深度学习,机器学习",
  "max_videos": 50,
  "enable_comments": true,
  "enable_sub_comments": true,
  "max_comments_per_video": 200
}
```
**说明**: 搜索多个关键词，爬取50个视频，每个视频最多200条评论

### 示例4: 仅爬取视频信息
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "ChatGPT",
  "max_videos": 100,
  "enable_comments": false
}
```
**说明**: 快速爬取100个视频的基本信息，不爬取任何评论

## 🚀 cURL 命令示例

### 1. 精简爬取
```bash
curl -X POST http://1.15.151.6:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "search",
    "keywords": "人工智能",
    "max_videos": 20,
    "enable_comments": false
  }'
```

### 2. 限制评论数量
```bash
curl -X POST http://1.15.151.6:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd"],
    "enable_comments": true,
    "max_comments_per_video": 50
  }'
```

## 🐍 Python 示例

```python
import requests
import json

BASE_URL = "http://1.15.151.6:8080"
HEADERS = {"Content-Type": "application/json"}

def create_custom_crawl_task():
    """创建自定义爬取任务"""
    data = {
        "platform": "bili",
        "type": "search",
        "keywords": "人工智能",
        "max_videos": 30,
        "enable_comments": True,
        "enable_sub_comments": False,
        "max_comments_per_video": 100
    }
    
    response = requests.post(f"{BASE_URL}/tasks", 
                           headers=HEADERS, 
                           data=json.dumps(data))
    return response.json()

# 使用示例
result = create_custom_crawl_task()
print("任务创建结果:", result)
```

## ⚡ 性能优化建议

### 1. 快速预览模式
```json
{
  "max_videos": 10,
  "enable_comments": false,
  "enable_sub_comments": false
}
```
**适用场景**: 快速了解内容概况

### 2. 平衡模式
```json
{
  "max_videos": 50,
  "enable_comments": true,
  "enable_sub_comments": false,
  "max_comments_per_video": 50
}
```
**适用场景**: 获取适量数据进行分析

### 3. 深度分析模式
```json
{
  "max_videos": 20,
  "enable_comments": true,
  "enable_sub_comments": true,
  "max_comments_per_video": 200
}
```
**适用场景**: 深度分析特定内容

## 📊 参数组合建议

| 使用场景 | max_videos | enable_comments | enable_sub_comments | max_comments_per_video |
|----------|------------|-----------------|---------------------|------------------------|
| 快速浏览 | 20-50 | false | false | 0 |
| 内容分析 | 30-100 | true | false | 50-100 |
| 深度研究 | 10-30 | true | true | 100-300 |
| 大规模采集 | 100+ | true | false | 20-50 |

## ⚠️ 注意事项

1. **性能影响**: 
   - 爬取评论会显著增加执行时间
   - 二级评论会进一步增加数据量和时间

2. **数据量控制**:
   - 建议根据实际需求设置合理的参数值
   - 避免设置过大的数值导致长时间运行

3. **服务器资源**:
   - 大量评论爬取会消耗更多服务器资源
   - 建议在非高峰时段进行大规模爬取

4. **合规使用**:
   - 请遵守平台使用条款
   - 合理控制爬取频率和数量

## 🔄 参数更新

这些参数会在每个任务执行时动态应用，不会影响其他正在运行的任务或默认配置。

---

**版本**: v1.1  
**更新时间**: 2025-07-06  
**新增功能**: ✅ 动态爬取参数配置支持
