# MediaCrawlerPro API 快速参考

## 🚀 基础信息
- **服务地址**: `http://1.15.151.6:8080`
- **Content-Type**: `application/json`

## 📡 核心端点

### 健康检查
```bash
curl -X GET http://1.15.151.6:8080/health
```

### 创建任务
```bash
curl -X POST http://1.15.151.6:8080/tasks \
  -H "Content-Type: application/json" \
  -d '请求体JSON'
```

### 查询任务
```bash
# 单个任务
curl -X GET http://1.15.151.6:8080/tasks/{task_id}

# 所有任务
curl -X GET http://1.15.151.6:8080/tasks
```

## 🎯 任务类型速查

### 1. BV号详细信息 (推荐)
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"]
}
```

### 2. 关键词搜索
```json
{
  "platform": "bili", 
  "type": "search",
  "keywords": "人工智能"
}
```

### 3. UP主视频
```json
{
  "platform": "bili",
  "type": "creator", 
  "uid_json_file": "/path/to/uid_list.json"
}
```

### 4. 首页推荐
```json
{
  "platform": "bili",
  "type": "homefeed"
}
```

### 5. 复杂JSON数据
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_data": [您的复杂JSON结构]
}
```

## 📊 响应状态

### 创建成功
```json
{
  "task_id": "uuid-string",
  "status": "created", 
  "message": "任务已创建并开始执行"
}
```

### 任务状态
- `pending` - 等待执行
- `running` - 正在执行  
- `completed` - 执行完成
- `failed` - 执行失败
- `cancelled` - 已取消

## ⚡ 快速测试

### 测试健康状态
```bash
curl http://1.15.151.6:8080/health
```

### 测试BV号爬取
```bash
curl -X POST http://1.15.151.6:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail", 
    "bv_list": ["BV1aRKHz2Ejd"]
  }'
```

### 测试关键词搜索
```bash
curl -X POST http://1.15.151.6:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "search",
    "keywords": "人工智能"
  }'
```

## 🔍 常用查询

### 获取最新任务状态
```bash
# 先获取所有任务
curl http://1.15.151.6:8080/tasks

# 然后查询特定任务
curl http://1.15.151.6:8080/tasks/TASK_ID
```

## ⚠️ 注意事项

1. **BV号格式**: 必须以"BV"开头
2. **文件路径**: 必须是服务器可访问的绝对路径  
3. **关键词**: 多个关键词用逗号分隔
4. **并发限制**: 建议同时运行任务不超过5个
5. **数据存储**: 结果保存在MySQL数据库中

## 🛠️ 故障排查

### 400 错误
- 检查JSON格式是否正确
- 验证必需参数是否提供
- 确认BV号格式是否有效

### 500 错误  
- 检查服务器状态
- 查看服务器日志
- 验证网络连接

### 任务失败
- 查询任务详细状态
- 检查错误信息
- 重新提交任务

---
**快速参考 v1.0** | **更新**: 2025-07-05
