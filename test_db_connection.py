#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接脚本
"""

import mysql.connector
import sys

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'port': 3307,
    'user': 'root',
    'password': '123456',
    'database': 'media_crawler',
    'charset': 'utf8mb4'
}

def test_connection():
    """测试数据库连接"""
    print("正在测试数据库连接...")
    print(f"主机: {DB_CONFIG['host']}")
    print(f"端口: {DB_CONFIG['port']}")
    print(f"用户: {DB_CONFIG['user']}")
    print(f"数据库: {DB_CONFIG['database']}")
    print("-" * 50)
    
    try:
        # 尝试连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            print("✅ 数据库连接成功！")
            
            # 获取数据库信息
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"MySQL 版本: {version}")
            
            # 测试查询指定表的统计信息
            print("\n📊 查询表统计信息:")
            stats_query = """
            SELECT 
                'bilibili_video_comment' as table_name, 
                COUNT(*) as record_count 
            FROM bilibili_video_comment
            UNION ALL
            SELECT 
                'bilibili_video' as table_name, 
                COUNT(*) as record_count 
            FROM bilibili_video
            UNION ALL
            SELECT 
                'bilibili_up_info' as table_name, 
                COUNT(*) as record_count 
            FROM bilibili_up_info
            ORDER BY table_name;
            """
            
            cursor.execute(stats_query)
            results = cursor.fetchall()
            
            total_records = 0
            for table_name, record_count in results:
                print(f"  {table_name}: {record_count} 条记录")
                total_records += record_count
            
            print(f"\n总记录数: {total_records}")
            
            cursor.close()
            connection.close()
            print("\n✅ 数据库连接测试完成")
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

if __name__ == '__main__':
    success = test_connection()
    sys.exit(0 if success else 1)
