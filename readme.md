# 冷启动数据格式化服务器 - README

## 📋 概览
本服务器部署了冷启动数据格式化工具，用于将Word文档中的非结构化数据转换为CSV/Excel格式。

---

## 🖥️ 服务器信息

### 基本配置
- **服务器IP**: `**********`
- **操作系统**: Ubuntu 
- **实例ID**: `lhins-bc40x6fg`
- **实例名称**: ubuntu
- **地域可用区**: 上海 | 上海二区

### 硬件规格
- **CPU**: 2核
- **内存**: 4GB
- **存储**: SSD云硬盘 70GB
- **带宽**: 6Mbps
- **流量包**: 600GB/月
- **到期时间**: 2028-09-25 22:02:51

---

## 🔐 登录方式

### SSH 连接
```bash
# 使用密码登录
ssh ubuntu@**********

# 用户名: ubuntu
# 密码: Xinyuan12345678
```

### 备用连接方式 (推荐)
```bash
# 使用私钥登录 (推荐方式)
ssh -i /Users/<USER>/Desktop/n8n/miyao.pem ubuntu@**********
```

---

## 🚀 部署的应用

### 主应用: 冷启动数据格式化工具
- **应用名称**: 冷启动数据格式化Web服务
- **技术栈**: Flask + Python 3
- **运行端口**: **5001**
- **访问地址**: `http://**********:5001`
- **应用文件**: `web_app.py`

#### 应用功能
- Word文档(.docx)上传
- 非结构化数据解析
- 自动提取时间戳、标题、链接、备注信息
- 输出Excel格式(.xlsx)文件
- 支持批量数据处理

#### 依赖库
```
flask
python-docx
openpyxl
```

### 新增应用: n8n 工作流自动化平台
- **应用名称**: n8n 工作流自动化平台
- **技术栈**: Docker + Node.js
- **运行端口**: **5678**
- **访问地址**: `http://**********:5678`
- **容器名称**: `n8n`
- **版本**: `1.99.1`
- **部署时间**: 2025-06-28

#### 应用功能
- 可视化工作流编辑器
- 支持400+种应用集成
- 自动化业务流程
- API接口调用
- 数据处理和转换
- 定时任务执行

#### Docker配置
```bash
# 容器ID: 6e40e7bd8a88
# 镜像: n8nio/n8n:latest
# 数据卷: n8n_data
# 环境变量:
# - N8N_SECURE_COOKIE=false
# - N8N_RUNNERS_ENABLED=true
```

#### 管理命令
```bash
# 查看容器状态
docker ps | grep n8n

# 查看日志
docker logs n8n --tail 20

# 停止容器
docker stop n8n

# 启动容器
docker start n8n

# 重启容器
docker restart n8n
```

### 新增应用: 播放量获取服务 - n8n版
- **应用名称**: 播放量获取服务 - n8n版
- **技术栈**: Flask + Python 3.12
- **运行端口**: **5003**
- **访问地址**: `http://**********:5003`
- **应用文件**: `播放量_n8n版.py`
- **虚拟环境**: `playcount_env`
- **部署时间**: 2025-06-28

#### 应用功能
- 专为n8n工作流设计的播放量获取API
- 支持多维表格API数据格式解析
- 批量处理B站和抖音视频播放量获取
- 实时进度跟踪和状态查询
- Excel结果文件生成和下载
- JSON格式结果数据输出
- 内存处理，无文件缓存

#### API接口
- **主API**: `POST /api/batch-process` - 批量处理视频链接
- **状态查询**: `GET /status/{task_id}` - 查询任务状态
- **文件下载**: `GET /download/{task_id}` - 下载Excel结果
- **JSON结果**: `GET /api/results/{task_id}` - 获取JSON结果

#### 管理命令
```bash
# 查看进程状态
ps aux | grep 播放量_n8n版

# 停止服务
pkill -f 播放量_n8n版.py

# 启动服务
cd /home/<USER>/bin/activate && nohup python3 播放量_n8n版.py > playcount.log 2>&1 &

# 查看日志
tail -f /home/<USER>/playcount.log

# 查看虚拟环境
ls -la /home/<USER>/playcount_env/
```

### 新增应用: BV号提取服务
- **应用名称**: BV号提取服务
- **技术栈**: Flask + Python 3.12
- **运行端口**: **5004**
- **访问地址**: `http://**********:5004`
- **应用文件**: `BV号提取服务.py`
- **虚拟环境**: `bv_extract_env`
- **部署时间**: 2025-06-30

#### 应用功能
- 专门从B站视频链接中提取BV号
- 支持完整链接直接提取（正则匹配）
- 支持短链接重定向提取（b23.tv自动跳转）
- 批量处理飞书多维表格API数据格式
- 高成功率：长链接100%，短链接100%重定向成功
- Web界面API文档展示
- JSON格式结果输出

#### API接口
- **主API**: `POST /api/extract-bv` - 批量提取BV号
- **Web界面**: `GET /` - API文档和使用说明

#### 处理格式支持
- 飞书多维表格API返回格式
- 对象形式链接字段：`{"link": "...", "text": "...", "type": "url"}`
- 数组形式链接字段
- 直接字符串链接

#### 管理命令
```bash
# 查看进程状态
ps aux | grep BV

# 停止服务
pkill -f BV号提取服务.py

# 启动服务
cd /home/<USER>/bin/activate && nohup python3 BV号提取服务.py > bv_extract.log 2>&1 &

# 查看日志
tail -f /home/<USER>/bv_extract.log

# 查看虚拟环境
ls -la /home/<USER>/bv_extract_env/
```

### 新增应用: BV/AV号转换器API服务 v2.0 🆕
- **应用名称**: 哔哩哔哩 BV/AV 转换器 API v2.0
- **技术栈**: Flask + Python 3.12
- **运行端口**: **5002**
- **访问地址**: `http://**********:5002` (本地开发: `http://127.0.0.1:5002`)
- **应用文件**: `bilibili_bv_av_converter_api.py`
- **依赖文件**: `bv_converter_requirements.txt`
- **虚拟环境**: `bv_converter_env`
- **部署时间**: 2025-07-01
- **最新更新**: 2025-07-01 (v2.0 大批量处理版本)

### 新增应用: MediaCrawlerPro 媒体爬虫系统 🆕
- **应用名称**: MediaCrawlerPro 媒体爬虫系统
- **技术栈**: Docker + Python 3.9 + Flask + MySQL + Redis
- **运行端口**: **8080** (HTTP API), **3307** (MySQL数据库)
- **访问地址**: `http://**********:8080`
- **健康检查**: `http://**********:8080/health`
- **存储位置**: `/root/data/disk/MediaCrawlerPro/` (独立硬盘)
- **部署时间**: 2025-07-05
- **部署方式**: Docker Compose 离线部署包

#### 应用功能 🆕 v2.0 增强版
- 完整的BV号与AV号双向转换服务
- 基于哔哩哔哩官方算法实现
- 支持大小写不敏感的BV号处理
- **🆕 大批量转换**: 支持最多 **1000个** 项目同时处理
- **🆕 自动分批**: 超过100个自动分批处理，提升性能
- **🆕 批次追踪**: 每个结果包含批次信息
- 飞书多维表格格式专用处理
- RESTful API设计
- Web界面API文档展示
- 完整的错误处理和日志记录
- ✅ 已通过75条记录的飞书格式测试，成功率100%
- ✅ 支持1000条大批量处理，性能优化

#### MediaCrawlerPro 应用功能 🆕
- **多平台支持**: 支持哔哩哔哩(Bilibili)等主流媒体平台
- **批量爬取**: 支持BV号列表批量处理，一次处理多个视频
- **多格式支持**: 支持复杂JSON、简单JSON、TXT等多种输入格式
- **RESTful API**: 提供完整的HTTP API接口，支持任务创建、查询、监控
- **异步处理**: 任务在后台异步执行，支持实时状态查询
- **数据存储**: MySQL数据库存储爬取结果，Redis缓存提升性能
- **进度跟踪**: 实时显示爬取进度，支持断点续传
- **容器化部署**: Docker容器化部署，环境隔离，易于管理
- **离线部署**: 支持完全离线部署，无需外部网络依赖

#### API接口
- **API文档**: `GET /` - API说明和使用文档
- **通用转换**: `POST /convert` - 自动识别并转换AV/BV号
- **AV转BV**: `GET /av2bv/<num>` - 单个AV号转BV号
- **BV转AV**: `GET /bv2av/<code>` - 单个BV号转AV号
- **批量转换**: `POST /batch` - 批量处理多个转换 (最多1000个，自动分批)
- **飞书转换**: `POST /feishu-convert` - 专门处理飞书多维表格格式 (最多1000个，自动分批)
- **健康检查**: `GET /health` - 服务状态检查

#### MediaCrawlerPro API接口 🆕
- **健康检查**: `GET /health` - 服务状态检查
- **创建任务**: `POST /tasks` - 创建爬取任务
- **查询任务**: `GET /tasks/<task_id>` - 获取任务状态和进度
- **任务列表**: `GET /tasks` - 列出所有任务
- **取消任务**: `DELETE /tasks/<task_id>` - 取消指定任务
- **数据库**: MySQL端口3307，用户名root，密码123456

#### 技术特性
- 支持58进制编码算法
- 使用魔法字符串: `FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf`
- 位置映射: `[0,1,2,9,7,5,6,4,8,3,10,11]`
- XOR加密处理
- 完全兼容官方转换结果
- 专用虚拟环境确保依赖隔离
- 高可用性：75条记录测试100%成功率，支持1000条大批量处理

#### 管理命令
```bash
# 查看进程状态
ps aux | grep bilibili_bv_av_converter

# 停止服务
pkill -f bilibili_bv_av_converter_api.py

# 启动服务 (生产环境)
cd /home/<USER>/bin/activate && nohup python3 bilibili_bv_av_converter_api.py > bv_converter.log 2>&1 &

# 启动服务 (本地开发)
python3 bilibili_bv_av_converter_api.py

# 查看日志
tail -f /home/<USER>/bv_converter.log

# 查看虚拟环境
ls -la /home/<USER>/bv_converter_env/

# 测试服务
curl http://127.0.0.1:5002/health
curl http://127.0.0.1:5002/av2bv/170001
curl http://127.0.0.1:5002/bv2av/BV17x411w7KC

# 测试飞书格式转换
curl -X POST http://127.0.0.1:5002/feishu-convert -H "Content-Type: application/json" -d @bv测试.json
```

#### MediaCrawlerPro 管理命令 🆕
```bash
# 进入部署目录
cd /root/data/disk/MediaCrawlerPro/MediaCrawlerPro-Offline-Deploy

# 查看服务状态
sudo docker-compose ps

# 启动所有服务
sudo docker-compose up -d

# 停止所有服务
sudo docker-compose down

# 重启服务
sudo docker-compose restart

# 查看日志
sudo docker-compose logs -f

# 查看特定服务日志
sudo docker-compose logs -f app
sudo docker-compose logs -f db

# 健康检查
curl http://**********:8080/health

# 创建爬取任务示例
curl -X POST http://**********:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_complex_json_file": "/path/to/bv.json"}'
```

---

## 📁 文件存储位置

### 项目根目录
```
/home/<USER>/data_format_app/
```

### 关键文件结构
```
data_format_app/
├── web_app.py              # Web应用主文件 (27.4KB)
└── venv/                   # Python虚拟环境目录
    ├── bin/                # 可执行文件
    ├── lib/                # 依赖库
    └── include/            # 头文件
```

### MediaCrawlerPro 存储位置 🆕
```
/root/data/disk/MediaCrawlerPro/                    # 独立硬盘存储 (100GB)
├── MediaCrawlerPro-Deploy.tar.gz                   # 原始部署包 (1.4GB)
└── MediaCrawlerPro-Offline-Deploy/                 # 解压后的部署目录
    ├── docker-compose.yaml                         # Docker编排文件
    ├── deploy.sh                                    # 部署脚本
    ├── load-images.sh                               # 镜像加载脚本
    ├── README.md                                    # 部署说明文档
    ├── QUICK_START.md                               # 快速开始指南
    ├── config/                                      # 配置文件目录
    ├── schema/                                      # 数据库初始化脚本
    ├── mediacrawlerpro.tar                          # 主应用镜像 (1.1GB)
    ├── mysql.tar                                    # MySQL镜像 (768MB)
    ├── redis.tar                                    # Redis镜像 (131MB)
    └── signsrv.tar                                  # 签名服务镜像 (1.0GB)
```

### 独立硬盘使用情况 🆕
- **设备**: /dev/vdb (100GB SSD)
- **挂载点**: /root/data/disk
- **已使用**: 4.9GB (5%)
- **可用空间**: 89GB (95%)
- **文件系统**: ext4

### 本地开发文件（非服务器）
```
/Users/<USER>/Desktop/冷启动数据格式化/
├── 1.py                   # 数据处理脚本v1
├── 2.py                   # 数据处理脚本v2
├── readme.md              # 本文件
├── 连接服务器             # 服务器连接信息
├── 数据格式.txt           # 原始数据格式
├── 示例输出数据.csv       # 示例输出格式
├── 示例输出数据.xlsx      # 示例输出格式
├── 待处理.docx           # 待处理Word文档
├── 待处理_处理结果.xlsx   # 处理结果文件
└── 输出数据.csv          # 最终输出数据
```

---

## 🔌 端口占用情况

| 端口 | 应用 | 状态 | 协议 | 容器/进程ID |
|------|------|------|------|------|
| **5001** | 冷启动数据格式化Web服务 | ✅ 运行中 | HTTP | 6527, 6529 |
| **5002** | BV/AV号转换器API服务 | ✅ 运行中 | HTTP | 2133079, 2133080 (Python) |
| **5003** | 播放量获取服务 - n8n版 | ✅ 运行中 | HTTP | 614503 (Python) |
| **5004** | BV号提取服务 | ✅ 运行中 | HTTP | 1366853 (Python) |
| **5678** | n8n 工作流自动化平台 | ✅ 运行中 | HTTP | 6e40e7bd8a88 (Docker) |
| 5679 | n8n Task Broker | ✅ 运行中 | TCP | 内部服务 |
| **8080** | MediaCrawlerPro HTTP API | 🔄 重启中 | HTTP | mediacrawlerpro (Docker) |
| **3307** | MediaCrawlerPro MySQL数据库 | 🔄 重启中 | MySQL | mysql_db (Docker) |
| 22 | SSH服务 | ✅ 运行中 | SSH | - |
| 53 | DNS服务 | ✅ 运行中 | UDP/TCP | - |

### ⚠️ 端口注意事项
- **端口5001已被占用** (冷启动数据格式化服务)，请勿在此端口部署其他应用
- **端口5002已被占用** (BV/AV号转换器API服务)，请勿在此端口部署其他应用
- **端口5003已被占用** (播放量获取服务 - n8n版)，请勿在此端口部署其他应用
- **端口5004已被占用** (BV号提取服务)，请勿在此端口部署其他应用
- **端口5678已被占用** (n8n工作流平台)，请勿在此端口部署其他应用
- **端口5679已被占用** (n8n Task Broker内部服务)
- **端口8080已被占用** (MediaCrawlerPro HTTP API)，请勿在此端口部署其他应用
- **端口3307已被占用** (MediaCrawlerPro MySQL数据库)，请勿在此端口部署其他应用
- 部署新应用时建议使用端口: 5005-5677, 5680-5700, 8081-8090等
- 生产环境建议使用80(HTTP)或443(HTTPS)端口

---

## 🛠️ 应用管理

### 启动应用
```bash
cd /home/<USER>/data_format_app/
source venv/bin/activate  # 激活虚拟环境
python web_app.py
```

### 停止应用
```bash
# 方法1: 按 Ctrl+C 停止(如果在前台运行)

# 方法2: 查找并终止进程
ps aux | grep web_app.py
kill -9 6527 6529  # 当前运行的进程ID

# 方法3: 一键停止所有相关进程
pkill -f web_app.py
```

### 查看应用状态
```bash
# 检查端口占用
netstat -tulpn | grep :5001
# 或
lsof -i :5001
```

---

## 🔧 系统维护

### 磁盘使用情况
```bash
df -h  # 查看磁盘使用
du -sh /home/<USER>/data_format_app/  # 查看项目大小
```

### 内存使用情况
```bash
free -h  # 查看内存使用
top      # 查看进程资源占用
```

### 日志查看
```bash
# 如果应用有日志文件
tail -f /path/to/logfile
```

---

## 📊 监控建议

### 资源监控
- **CPU使用率**: 建议保持在80%以下
- **内存使用率**: 建议保持在80%以下  
- **磁盘使用率**: 建议保持在85%以下
- **网络流量**: 注意月流量600GB限制

### 备份建议
- 定期备份重要数据文件
- 备份应用配置和代码
- 建议使用git进行版本管理

---

## 🐳 Docker 配置

### Docker 镜像加速器
已配置腾讯云镜像加速器，提升镜像下载速度：
```json
{
  "registry-mirrors": ["https://mirror.ccs.tencentyun.com"]
}
```

### Docker 管理命令
```bash
# 查看所有容器
docker ps -a

# 查看Docker信息
docker info

# 清理未使用的镜像
docker image prune

# 查看数据卷
docker volume ls
```

---

## ⚡ 部署新应用时注意事项

1. **端口冲突**: 
   - 避免使用端口5001 (冷启动数据格式化服务)
   - 避免使用端口5002 (BV/AV号转换器API服务)
   - 避免使用端口5003-5004 (播放量服务、BV号提取服务)
   - 避免使用端口5678-5679 (n8n服务)
2. **资源使用**: 
   - 冷启动应用占用约100MB内存 (2个Python进程)
   - BV/AV转换器占用约50MB内存 (1个Python进程)
   - 播放量n8n版应用占用约170MB内存 (2个Python进程)
   - BV号提取服务占用约40MB内存 (1个Python进程)
   - n8n应用占用约200-300MB内存 (Docker容器)
3. **依赖冲突**: 注意Python库版本兼容性 (当前使用虚拟环境)
4. **文件权限**: 确保新应用有适当的文件读写权限
5. **防火墙**: 如需开放新端口，请配置防火墙规则
6. **虚拟环境**: 建议为新应用创建独立的虚拟环境
7. **Docker资源**: 注意Docker容器的资源占用和数据持久化

### 推荐端口范围
- Web应用: 5005-5677, 5680-5700, 8080-8090
- API服务: 3000-3010, 9000-9010
- 数据库: 3306(MySQL), 5432(PostgreSQL), 27017(MongoDB)

---

## 📞 联系信息
- **管理员**: Xinyuan
- **最后更新**: 2025年1月
- **文档版本**: v1.0

---

## 🔄 更新日志
- **2025-01**: 初始版本，记录冷启动数据格式化应用部署信息
- **2025-01-28**: 更新服务器实际路径信息，确认应用运行状态
- **2025-06-28**: 新增n8n工作流自动化平台部署，配置Docker镜像加速器，更新端口占用情况
- **2025-06-28**: 新增播放量获取服务n8n版部署，运行在端口5003，专为n8n工作流设计
- **2025-06-30**: 新增BV号提取服务部署，运行在端口5004，专为B站视频链接提取BV号
- **2025-07-01**: 新增BV/AV号转换器API服务v2.0，运行在端口5002，支持哔哩哔哩视频号码双向转换和飞书格式处理，已通过75条记录测试验证，升级支持1000条大批量处理和自动分批功能
- **2025-07-05**: 新增MediaCrawlerPro媒体爬虫系统，部署在独立硬盘(/root/data/disk)，使用Docker容器化部署，占用端口8080(HTTP API)和3307(MySQL)，支持哔哩哔哩视频批量爬取，提供RESTful API接口，包含MySQL数据库和Redis缓存，采用离线部署包方式实现无网络依赖部署
curl -X POST http://**********:8080/tasks   -H "Content-Type: application/json"   -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"],
    "enable_comments": true,
    "enable_sub_comments": true,
    "max_comments_per_video": 100
  }'