#!/bin/bash

# MediaCrawlerPro Bilibili数据清理工具 (简化版本)
# 通过SSH直接连接服务器执行清理命令

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器连接参数
SERVER_HOST="**********"
SERVER_USER="ubuntu"
SSH_KEY="/Users/<USER>/Desktop/MediaCrawlerPro/miyao.pem"

# 日志函数
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] ⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ❌ $1${NC}"
}

# 获取数据统计
get_table_stats() {
    log_info "获取Bilibili数据统计..."

    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
cd /root/data/disk/MediaCrawlerPro
echo "📊 Bilibili数据统计"
echo "===================="
docker exec mysql_db mysql -u root -p123456 media_crawler -e "
SELECT 
    'bilibili_video_comment' as '表名', 
    COUNT(*) as '记录数' 
FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as '表名', 
    COUNT(*) as '记录数' 
FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as '表名', 
    COUNT(*) as '记录数' 
FROM bilibili_up_info;
"
EOF
}

# 清理Bilibili数据
clear_bilibili_data() {
    log_warning "即将清理Bilibili平台所有数据！"
    echo -e "${YELLOW}包括：视频信息、评论数据、UP主信息${NC}"
    echo -n "确认继续？(y/N): "
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    log_info "开始清理Bilibili数据..."

    # 通过SSH执行Docker命令
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
cd /root/data/disk/MediaCrawlerPro
echo "🗑️ 开始清理Bilibili数据"
echo "========================"

# 执行清理命令
docker exec mysql_db mysql -u root -p123456 media_crawler -e "
-- 开始事务
START TRANSACTION;

-- 清理前统计
SELECT '开始清理Bilibili数据' as '状态';

-- 1. 清理评论数据
SELECT '正在清理评论数据...' as '操作';
DELETE FROM bilibili_video_comment;
SELECT CONCAT('清理评论: ', ROW_COUNT(), ' 条') as '结果';

-- 2. 清理视频数据  
SELECT '正在清理视频数据...' as '操作';
DELETE FROM bilibili_video;
SELECT CONCAT('清理视频: ', ROW_COUNT(), ' 条') as '结果';

-- 3. 清理UP主数据
SELECT '正在清理UP主数据...' as '操作';
DELETE FROM bilibili_up_info;
SELECT CONCAT('清理UP主: ', ROW_COUNT(), ' 条') as '结果';

-- 提交事务
COMMIT;

-- 验证清理结果
SELECT '清理后统计' as '状态';
SELECT 
    'bilibili_video_comment' as '表名', 
    COUNT(*) as '剩余记录' 
FROM bilibili_video_comment
UNION ALL
SELECT 
    'bilibili_video' as '表名', 
    COUNT(*) as '剩余记录' 
FROM bilibili_video
UNION ALL
SELECT 
    'bilibili_up_info' as '表名', 
    COUNT(*) as '剩余记录' 
FROM bilibili_up_info;

SELECT 'Bilibili数据清理完成！' as '状态';
"

echo "✅ 数据清理操作完成！"
EOF

    if [ $? -eq 0 ]; then
        log_success "Bilibili数据清理完成！"
    else
        log_error "数据清理过程中出现错误"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "MediaCrawlerPro Bilibili数据清理工具 (简化版本)"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --stats     仅显示数据统计，不执行清理"
    echo "  -c, --clear     执行数据清理"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  此版本通过SSH密钥连接服务器，使用Docker容器内的MySQL客户端"
    echo "  无需手动输入密码，使用SSH密钥认证"
    echo ""
    echo "示例:"
    echo "  $0 --stats      # 查看数据统计"
    echo "  $0 --clear      # 清理数据"
    echo ""
    echo "等效Python命令:"
    echo "  python3 quick_clear_all_data.py --platform bilibili"
}

# 主函数
main() {
    echo "🧹 MediaCrawlerPro Bilibili数据清理工具 (简化版本)"
    echo "=========================================================="
    
    # 检查参数
    case "$1" in
        -s|--stats)
            get_table_stats
            ;;
        -c|--clear)
            get_table_stats
            echo ""
            clear_bilibili_data
            ;;
        -h|--help)
            show_help
            ;;
        "")
            log_warning "请指定操作参数"
            show_help
            exit 1
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
