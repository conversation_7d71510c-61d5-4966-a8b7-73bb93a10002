2025-07-08 14:20:10,804 - INFO - 启动 MediaCrawlerPro 数据库清空 API 服务
2025-07-08 14:20:10,805 - INFO - 允许清空的表: ['bilibili_video_comment', 'bilibili_video', 'bilibili_up_info']
2025-07-08 14:20:10,805 - INFO - 数据库配置: **********:3307/media_crawler
2025-07-08 14:20:10,829 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://*************:8081
2025-07-08 14:20:10,829 - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 14:20:34,593 - INFO - 127.0.0.1 - - [08/Jul/2025 14:20:34] "GET /health HTTP/1.1" 200 -
2025-07-08 14:20:43,176 - INFO - 获取表统计信息成功，总记录数: 4150
2025-07-08 14:20:43,177 - INFO - 127.0.0.1 - - [08/Jul/2025 14:20:43] "GET /tables/stats HTTP/1.1" 200 -
