#!/bin/bash
# MediaCrawlerPro 数据库清空API部署脚本

set -e

echo "🚀 开始部署 MediaCrawlerPro 数据库清空 API..."

# 创建部署目录
DEPLOY_DIR="/root/data/disk/MediaCrawlerPro/clear_api"
echo "📁 创建部署目录: $DEPLOY_DIR"
sudo mkdir -p $DEPLOY_DIR

# 复制文件
echo "📋 复制API文件..."
sudo cp database_clear_api.py $DEPLOY_DIR/
sudo chmod +x $DEPLOY_DIR/database_clear_api.py

# 安装Python依赖
echo "📦 安装Python依赖..."
sudo apt update
sudo apt install -y python3 python3-pip python3-venv

# 创建虚拟环境
echo "🐍 创建Python虚拟环境..."
sudo bash -c "cd $DEPLOY_DIR && python3 -m venv venv"
sudo $DEPLOY_DIR/venv/bin/pip install flask mysql-connector-python python-dotenv

# 创建systemd服务文件
echo "⚙️ 创建systemd服务..."
sudo tee /etc/systemd/system/mediacrawler-clear-api.service > /dev/null <<EOF
[Unit]
Description=MediaCrawlerPro Database Clear API
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/venv/bin/python $DEPLOY_DIR/database_clear_api.py
Restart=always
RestartSec=10
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd并启动服务
echo "🔄 启动服务..."
sudo systemctl daemon-reload
sudo systemctl enable mediacrawler-clear-api
sudo systemctl start mediacrawler-clear-api

# 检查服务状态
echo "✅ 检查服务状态..."
sudo systemctl status mediacrawler-clear-api --no-pager

echo "🎉 部署完成！"
echo "📍 API服务地址: http://**********:8081"
echo "🔑 API密钥: mediacrawler_clear_2024"
echo ""
echo "📋 可用接口:"
echo "  GET  /health                    - 健康检查"
echo "  GET  /tables/stats              - 获取表统计"
echo "  POST /tables/clear              - 清空所有表"
echo "  DELETE /tables/clear/<table>    - 清空单个表"
echo ""
echo "💡 使用示例:"
echo "  curl -H 'X-API-Key: mediacrawler_clear_2024' http://**********:8081/tables/stats"
