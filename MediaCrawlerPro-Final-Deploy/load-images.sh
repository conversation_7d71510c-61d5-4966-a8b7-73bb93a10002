#!/bin/bash

# MediaCrawlerPro 离线部署 - 镜像加载脚本
# 此脚本用于在目标服务器上加载所有Docker镜像

echo "=== MediaCrawlerPro 离线部署 - 镜像加载 ==="
echo "开始加载Docker镜像..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    echo "Ubuntu安装命令: sudo apt update && sudo apt install -y docker.io docker-compose"
    exit 1
fi

# 检查Docker服务是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker服务未运行，请启动Docker服务"
    echo "启动命令: sudo systemctl start docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 加载镜像
echo "📦 正在加载主应用镜像..."
docker load -i mediacrawlerpro.tar
if [ $? -eq 0 ]; then
    echo "✅ 主应用镜像加载成功"
else
    echo "❌ 主应用镜像加载失败"
    exit 1
fi

echo "📦 正在加载MySQL镜像..."
docker load -i mysql.tar
if [ $? -eq 0 ]; then
    echo "✅ MySQL镜像加载成功"
else
    echo "❌ MySQL镜像加载失败"
    exit 1
fi

echo "📦 正在加载Redis镜像..."
docker load -i redis.tar
if [ $? -eq 0 ]; then
    echo "✅ Redis镜像加载成功"
else
    echo "❌ Redis镜像加载失败"
    exit 1
fi

echo "📦 正在加载签名服务镜像..."
docker load -i signsrv.tar
if [ $? -eq 0 ]; then
    echo "✅ 签名服务镜像加载成功"
else
    echo "❌ 签名服务镜像加载失败"
    exit 1
fi

echo ""
echo "🎉 所有镜像加载完成！"
echo ""
echo "📋 已加载的镜像列表:"
docker images | grep -E "(mediacrawlerpro|mysql|redis)"
echo ""
echo "🚀 现在可以运行 './deploy.sh' 启动服务"
