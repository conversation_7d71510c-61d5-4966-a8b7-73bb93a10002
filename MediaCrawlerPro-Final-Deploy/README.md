# MediaCrawlerPro 最终部署包

## 📦 包含内容

本部署包包含MediaCrawlerPro的最新修复版本，支持HTTP API直接调用，无需文件依赖。

### 🐳 Docker镜像文件
- `mediacrawlerpro.tar` - 主程序镜像 (v3-signsrv-fixed)
- `mysql.tar` - MySQL 8.0 数据库镜像
- `redis.tar` - Redis 7.0 缓存镜像  
- `signsrv.tar` - 签名服务镜像

### 📋 配置文件
- `docker-compose.yaml` - 容器编排配置
- `config/` - 应用配置文件目录
- `schema/` - 数据库初始化脚本

### 🚀 部署脚本
- `load-images.sh` - 镜像加载脚本
- `deploy.sh` - 一键部署脚本

## ✨ 新版本特性

### 🔧 修复内容
- ✅ 修复签名服务连接问题
- ✅ 支持HTTP API直接调用
- ✅ 无需BV文件依赖
- ✅ 自动临时文件管理

### 🌐 API功能
- **健康检查**: `GET /health`
- **创建任务**: `POST /tasks`
- **查询任务**: `GET /tasks/{task_id}`
- **删除任务**: `DELETE /tasks/{task_id}`

### 📝 API使用示例

#### 1. 简单BV数组
```bash
curl -X POST http://服务器IP:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"]
  }'
```

#### 2. 复杂JSON数据
```bash
curl -X POST http://服务器IP:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_complex_data": [复杂JSON结构]
  }'
```

## 🚀 快速部署

### 1. 上传部署包
```bash
# 将整个目录上传到服务器
scp -r MediaCrawlerPro-Final-Deploy ubuntu@服务器IP:~/
```

### 2. 加载镜像
```bash
cd MediaCrawlerPro-Final-Deploy
chmod +x *.sh
./load-images.sh
```

### 3. 启动服务
```bash
./deploy.sh
```

### 4. 验证部署
```bash
# 检查服务状态
curl http://localhost:8080/health

# 测试API
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail", 
    "bv_list": ["BV1aRKHz2Ejd"]
  }'
```

## 🔧 系统要求

- **操作系统**: Ubuntu 18.04+ / CentOS 7+
- **Docker**: 20.10+
- **内存**: 4GB+
- **存储**: 10GB+
- **网络**: 需要访问B站API

## 📊 端口说明

- **8080**: HTTP API端口 (公网访问)
- **3307**: MySQL数据库端口 (可选外部访问)
- **8989**: 签名服务端口 (内部通信)

## 🔍 故障排除

### 查看日志
```bash
# 查看主程序日志
docker logs mediacrawlerpro

# 查看所有服务状态
docker ps
```

### 重启服务
```bash
# 停止所有服务
docker-compose down

# 重新启动
./deploy.sh
```

## 📞 技术支持

如遇问题，请检查：
1. Docker服务是否正常运行
2. 端口是否被占用
3. 网络连接是否正常
4. 服务器资源是否充足

---

**版本**: v3-signsrv-fixed  
**更新时间**: 2025-07-06  
**兼容性**: x86_64 架构
