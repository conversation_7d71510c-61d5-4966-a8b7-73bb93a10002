# MediaCrawlerPro 快速开始指南

## 🚀 5分钟快速部署

### 步骤1: 上传文件
```bash
# 将部署包上传到服务器
scp -r MediaCrawlerPro-Final-Deploy ubuntu@你的服务器IP:~/
```

### 步骤2: 连接服务器
```bash
ssh ubuntu@你的服务器IP
cd MediaCrawlerPro-Final-Deploy
```

### 步骤3: 一键部署
```bash
# 给脚本执行权限
chmod +x *.sh

# 加载Docker镜像
./load-images.sh

# 启动所有服务
./deploy.sh
```

### 步骤4: 验证部署
```bash
# 检查服务健康状态
curl http://localhost:8080/health

# 应该返回: {"service":"MediaCrawlerPro HTTP Middleware","status":"ok","timestamp":"..."}
```

## 🎯 API测试示例

### 测试单个BV号
```bash
curl -X POST http://你的服务器IP:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd"]
  }'
```

### 测试多个BV号
```bash
curl -X POST http://你的服务器IP:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili", 
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg", "BV1zvK9zSEvD"]
  }'
```

### 查询任务状态
```bash
# 使用返回的task_id查询
curl http://你的服务器IP:8080/tasks/任务ID
```

## 📊 服务端口

- **8080**: HTTP API (公网访问)
- **3307**: MySQL数据库 (可选)

## 🔧 常用命令

### 查看服务状态
```bash
docker ps
```

### 查看日志
```bash
# 主程序日志
docker logs mediacrawlerpro

# 数据库日志  
docker logs mysql_db

# 签名服务日志
docker logs mediacrawler_signsrv
```

### 重启服务
```bash
docker-compose down
./deploy.sh
```

### 停止服务
```bash
docker-compose down
```

## ⚠️ 注意事项

1. **防火墙设置**: 确保8080端口对外开放
2. **资源要求**: 至少4GB内存，10GB存储空间
3. **网络连接**: 需要能访问B站API
4. **Docker版本**: 建议使用Docker 20.10+

## 🆘 常见问题

### Q: 端口被占用怎么办？
```bash
# 查看端口占用
sudo netstat -tlnp | grep 8080

# 修改docker-compose.yaml中的端口映射
```

### Q: 镜像加载失败？
```bash
# 检查磁盘空间
df -h

# 清理Docker缓存
docker system prune -f
```

### Q: 服务启动失败？
```bash
# 查看详细错误日志
docker-compose logs

# 检查配置文件
cat docker-compose.yaml
```

---

🎉 **部署成功后，您就可以通过HTTP API直接调用MediaCrawlerPro进行B站数据爬取了！**
