#!/bin/bash

# MediaCrawlerPro 离线部署脚本
# 此脚本用于在目标服务器上部署MediaCrawlerPro系统

echo "=== MediaCrawlerPro 离线部署 ==="

# 检查Docker和Docker Compose是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装，请先安装Docker"
    echo "Ubuntu安装命令: sudo apt update && sudo apt install -y docker.io docker-compose"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ 错误: Docker Compose未安装，请先安装Docker Compose"
    echo "Ubuntu安装命令: sudo apt install -y docker-compose"
    exit 1
fi

# 检查Docker服务是否运行
if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker服务未运行，请启动Docker服务"
    echo "启动命令: sudo systemctl start docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 检查镜像是否已加载
echo "🔍 检查Docker镜像..."
if ! docker images | grep -q "mediacrawlerpro"; then
    echo "❌ 错误: 主应用镜像未找到，请先运行 './load-images.sh' 加载镜像"
    exit 1
fi

if ! docker images | grep -q "mysql"; then
    echo "❌ 错误: MySQL镜像未找到，请先运行 './load-images.sh' 加载镜像"
    exit 1
fi

if ! docker images | grep -q "redis"; then
    echo "❌ 错误: Redis镜像未找到，请先运行 './load-images.sh' 加载镜像"
    exit 1
fi

echo "✅ 所有必需镜像已就绪"

# 停止现有服务（如果存在）
echo "🛑 停止现有服务..."
docker-compose down 2>/dev/null || true

# 启动服务
echo "🚀 启动MediaCrawlerPro服务..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 MediaCrawlerPro部署成功！"
    echo ""
    echo "📋 服务状态:"
    docker-compose ps
    echo ""
    echo "🌐 访问信息:"
    echo "  - HTTP API接口: http://服务器IP:8080"
    echo "  - MySQL数据库: 服务器IP:3307 (用户名: root, 密码: 123456)"
    echo ""
    echo "📖 使用说明:"
    echo "  1. 等待所有服务启动完成（约30-60秒）"
    echo "  2. 访问 http://服务器IP:8080/health 检查服务状态"
    echo "  3. 使用HTTP API创建爬取任务"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
else
    echo "❌ 部署失败，请检查错误信息"
    exit 1
fi
