# ==================== MediaCrawlerPro 命令行工具 ====================

# 1. 进入项目目录
cd /Users/<USER>/Desktop/MediaCrawlerPro/MediaCrawlerPro-Python

# ==================== B站(bilibili)平台爬取命令 ====================

# --- 基本爬取命令 ---

# 1. 关键词搜索爬取
python3 main.py --platform bili --type search

# 2. 指定视频详情爬取（需要在config/base_config.py中设置BILI_SPECIFIED_ID_LIST）
python3 main.py --platform bili --type detail

# 2.1 批量爬取BV号视频详情（使用JSON文件）
python3 main.py --platform bili --type detail --bili_bv_json "/Users/<USER>/Desktop/MediaCrawlerPro/bv_list.json"

# 2.2 批量爬取BV号视频详情（使用TXT文件，推荐）
python3 main.py --platform bili --type detail --bili_bv_txt "/Users/<USER>/Desktop/MediaCrawlerPro/bv_list.txt"

# 2.3 批量爬取BV号视频详情（使用现有TXT文件）
python3 main.py --platform bili --type detail --bili_bv_txt "/Users/<USER>/Desktop/MediaCrawlerPro/齐鸟收录后40.txt"

# 2.4 批量爬取BV号视频详情（使用复杂格式JSON文件）
python3 main.py --platform bili --type detail --bili_bv_complex_json "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"

# 3. UP主主页数据爬取（需要在config/base_config.py中设置BILI_CREATOR_ID_LIST）
python3 main.py --platform bili --type creator

# 4. 首页推荐内容爬取
python3 main.py --platform bili --type homefeed

# --- 批量爬取命令 ---

# 5. 批量爬取UP主数据（使用JSON文件）
python3 main.py --platform bili --type creator --bili_uid_json "/Users/<USER>/Desktop/MediaCrawlerPro/1.json"

# --- 关键词自动爬取命令 ---

# 6. 关键词自动爬取（10秒间隔）
python3 keyword_auto_crawler.py keywords.txt --platform bili --wait-time 10

# 7. 关键词自动爬取（30秒间隔）
python3 keyword_auto_crawler.py keywords.txt --platform bili --wait-time 30

# --- 数据管理命令 ---

# 8. 查看数据库状态
python3 quick_clear_all_data.py --status

# 9. 清理B站数据
python3 quick_clear_all_data.py --platform bilibili

# 10. 清理所有平台数据
python3 quick_clear_all_data.py --all

# 11. 清理B站评论数据
python3 clear_bilibili_comments.py

# --- 进度管理命令 ---

# 12. 查看批量爬取进度
python3 progress_manager.py --status

# 13. 重置失败的UID（允许重新处理）
python3 progress_manager.py --reset-failed

# 14. 清理进度文件
python3 progress_manager.py --clean

# --- 失败任务重新处理 ---

# 15. 重新运行失败的UP主任务（自动跳过成功的）
python3 main.py --platform bili --type creator --bili_uid_json "/Users/<USER>/Desktop/MediaCrawlerPro/1.json"

# 16. 重新运行失败的BV号任务（自动跳过成功的）
python3 main.py --platform bili --type detail --bili_bv_txt "/Users/<USER>/Desktop/MediaCrawlerPro/齐鸟收录后40.txt"

# 17. 重新运行失败的BV号任务（JSON格式）
python3 main.py --platform bili --type detail --bili_bv_json "/Users/<USER>/Desktop/MediaCrawlerPro/bv_list.json"

# 18. 重新运行失败的BV号任务（复杂JSON格式）
python3 main.py --platform bili --type detail --bili_bv_complex_json "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"

# --- 邮件通知测试 ---

# 19. 测试邮件配置
python3 test_email_config.py

# 20. Windows系统邮件问题排除（如果遇到连接超时）
python3 test_email_config.py  # 会自动检测网络连接并提供Windows特定建议

# 21. SMTP连接诊断（详细测试各个端口和协议）
python3 test_smtp_connection.py

# ==================== HTTP中间件服务 ====================

# --- HTTP服务启动 ---

# 22. 启动HTTP中间件服务（默认端口8080）
python3 start_http_server.py

# 23. 启动HTTP中间件服务（自定义端口）
python3 http_middleware.py --host 0.0.0.0 --port 8080

# 24. 启动HTTP中间件服务（调试模式）
python3 http_middleware.py --host 127.0.0.1 --port 8080 --debug

# --- HTTP API测试 ---

# 25. 运行API使用示例
python3 http_api_examples.py

# 26. 健康检查
curl http://localhost:8080/health

# 27. 创建复杂JSON格式BV号爬取任务
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_complex_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"}'

# 28. 创建简单JSON格式BV号爬取任务
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv_list.json"}'

# 29. 创建TXT文件BV号爬取任务
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_txt_file": "/Users/<USER>/Desktop/MediaCrawlerPro/齐鸟收录后40.txt"}'

# 30. 创建关键词搜索任务
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "search", "keywords": "deepseek,chatgpt"}'

# 31. 获取任务状态（替换TASK_ID为实际任务ID）
curl http://localhost:8080/tasks/TASK_ID

# 32. 列出所有任务
curl http://localhost:8080/tasks

# 33. 取消任务（替换TASK_ID为实际任务ID）
curl -X DELETE http://localhost:8080/tasks/TASK_ID

# --- Web界面 ---

# 34. 打开Web控制台（需要先启动HTTP服务）
# 在浏览器中打开: file:///Users/<USER>/Desktop/MediaCrawlerPro/MediaCrawlerPro-Python/web_interface.html

# ==================== 配置说明 ====================
pip3 install yagmail
# 主要配置文件：
# - config/base_config.py：基础配置（关键词、爬取数量等）
# - config/db_config.py：数据库配置
# - config/proxy_config.py：代理配置
# - config/email_config.py：邮件配置

# 重要配置项：
# - KEYWORDS：搜索关键词，如 "deepseek,chatgpt"
# - CRAWLER_MAX_NOTES_COUNT：最大爬取数量（当前：120）
# - MAX_CONCURRENCY_NUM：并发数量（当前：5）
# - ENABLE_GET_COMMENTS：是否爬取评论（当前：True）
# - BILI_CREATOR_ID_LIST：指定UP主ID列表
# - BILI_SPECIFIED_ID_LIST：指定视频BVID列表
# - BILI_BV_JSON：BV号JSON文件路径
# - BILI_BV_TXT：BV号TXT文件路径
# - BILI_BV_COMPLEX_JSON：复杂格式BV号JSON文件路径

# 冷却机制配置项：
# - ENABLE_COOLDOWN：是否启用冷却机制（当前：True）
# - COOLDOWN_TRIGGER_COUNT：每处理多少个UP主后触发冷却（当前：300）
# - COOLDOWN_DURATION：冷却时间（秒）（当前：900秒 = 15分钟）
# - COOLDOWN_DISPLAY_INTERVAL：冷却时显示倒计时间隔（当前：60秒）

# 邮件通知配置项（config/email_config.py）：
# - ENABLE_EMAIL_NOTIFICATION：是否启用邮件通知（当前：True）
# - SENDER_EMAIL：发送方邮箱（需要配置真实邮箱）
# - SENDER_PASSWORD：邮箱应用密码（注意：不是登录密码）
# - RECEIVER_EMAILS：接收方邮箱列表（可配置多个）

# ==================== BV号爬取文件格式说明 ====================

# JSON文件格式示例（bv_list.json）：
# [
#   "BV1HzzGY3ExC",
#   "BV1T5q5YnEW4",
#   "BV1PS6PYtEpi"
# ]

# TXT文件格式示例（bv_list.txt）：
# BV1HzzGY3ExC
# https://www.bilibili.com/video/BV1T5q5YnEW4/
# BV1PS6PYtEpi
# https://www.bilibili.com/video/BV1abc123def/
#
# 注意：TXT文件支持以下格式：
# - 纯BV号：BV1HzzGY3ExC
# - 完整链接：https://www.bilibili.com/video/BV1HzzGY3ExC/
# - 程序会自动提取BV号并去重

# ==================== 示例数据 ====================

# UP主ID示例：434377496（配置在BILI_CREATOR_ID_LIST中）
# 视频BVID示例：BV1T5q5YnEW4, BV1PS6PYtEpi, BV1HzzGY3ExC

# ==================== 故障排除 ====================

# 如果遇到代理问题，可以在config/proxy_config.py中设置：
# ENABLE_IP_PROXY = False  # 禁用代理

# 如果遇到数据库连接问题，检查：
# - MySQL服务是否启动
# - Redis服务是否启动（docker start redis-server）
# - config/db_config.py中的数据库配置

# 如果遇到邮件发送问题：
# 1. 使用测试命令检查配置：python3 test_email_config.py
# 2. 确认邮箱应用密码正确（不是登录密码）
# 3. 检查网络连接和防火墙设置
# 4. 临时禁用邮件通知：在config/email_config.py中设置ENABLE_EMAIL_NOTIFICATION = False

# ==================== Windows系统邮件问题特殊排除 ====================

# Windows系统邮件连接超时问题（WinError 10060）：
# 1. 检查Windows防火墙设置：
#    - 打开"Windows Defender 防火墙"
#    - 点击"允许应用或功能通过Windows Defender防火墙"
#    - 确保Python.exe被允许通过防火墙（专用和公用网络）

# 2. 检查网络代理设置：
#    - 控制面板 -> 网络和Internet -> 代理
#    - 如果启用了代理，尝试临时禁用测试

# 3. 检查企业网络限制：
#    - 企业防火墙可能阻止SMTP端口（25, 465, 587）
#    - 联系网络管理员了解SMTP策略
#    - 尝试使用手机热点测试

# 4. 尝试不同的SMTP端口：
#    - QQ邮箱支持多个端口：465(SSL), 587(TLS), 25(TLS)
#    - 程序会自动尝试备用端口配置

# 5. 杀毒软件干扰：
#    - 部分杀毒软件会阻止Python的网络连接
#    - 临时禁用杀毒软件测试
#    - 将Python添加到杀毒软件白名单

# 6. DNS解析问题：
#    - 尝试更换DNS服务器（如*******）
#    - 刷新DNS缓存：ipconfig /flushdns

# 7. 临时解决方案：
#    - 更换邮箱服务商（163邮箱在某些网络环境下更稳定）
#    - 使用手机热点测试网络环境
#    - 设置 ENABLE_EMAIL_NOTIFICATION = False 暂时禁用邮件功能

# ==================== 已知问题记录 ====================

# 3493080168663298 - 报错（需要进一步调试）
# 3493091086436480 - 代理认证失败（已修复）

# ==================== 注意事项 ====================

# 1. 批量爬取已修改为：错误时只记录不重试
# 2. 账号间隔时间已修改为：随机4-6秒
# 3. 请合理控制爬取频率，遵守平台使用条款
# 4. 建议定期备份数据库数据
# 5. 使用前确保所有依赖服务正常运行（MySQL、Redis）
# 6. 邮件通知功能可选，配置后将自动发送任务完成通知

# ==================== 进度文件管理说明 ====================

# 进度文件删除逻辑（新版本）：
# - 全部任务成功完成：自动备份并删除进度文件（允许下次从头开始）
# - 存在失败任务：保留进度文件（方便重新处理失败项）
# - 手动清理：使用 progress_manager.py --clean 强制删除

# 失败任务处理流程：
# 1. 程序完成后会显示失败任务列表和处理指导
# 2. 直接重新运行相同命令，自动跳过成功任务
# 3. 或使用 progress_manager.py 工具管理进度
docker run -d --name redis-server -p 6380:6379 redis:7.0 redis-server --requirepass 123456


 python3 main.py --platform bili --type detail --bili_bv_txt "/Users/<USER>/Desktop/MediaCrawlerPro/齐鸟收录后40.txt"

构建一个av bv互转库


curl -X POST http://1.15.151.6:8080/tasks   -H "Content-Type: application/json"   -d '{
    "platform": "bili",
    "type": "detail",
    "bv_list": ["BV1aRKHz2Ejd", "BV1ceK9zHEpg"],
    "enable_comments": true,
    "enable_sub_comments": true,
    "max_comments_per_video": 100
  }'