# 数据库清理工具使用说明

## 概述

`quick_clear_all_data.py` 是一个全面的数据库清理工具，能够清空MediaCrawlerPro数据库中的视频信息、评论数据等所有相关内容。支持多平台数据清理，提供灵活的清理选项。

## 功能特点

- 🌐 **多平台支持**: 支持所有主流平台的数据清理
- 🎯 **精确控制**: 可选择清理特定平台或所有平台
- 📊 **状态查看**: 实时查看数据库状态和数据量统计
- 🔄 **智能清理**: 按正确顺序清理数据（评论→内容→创作者）
- ✅ **安全验证**: 清理后自动验证结果
- 📝 **详细日志**: 实时显示清理进度和结果

## 支持的平台

| 平台 | 代码 | 清理的表 |
|------|------|----------|
| B站 | bilibili | bilibili_video, bilibili_video_comment, bilibili_creator |
| 小红书 | xiaohongshu | xhs_note, xhs_note_comment, xhs_creator |
| 抖音 | douyin | douyin_aweme, douyin_aweme_comment, douyin_creator |
| 快手 | kuaishou | kuaishou_video, kuaishou_video_comment, kuaishou_creator |
| 微博 | weibo | weibo_note, weibo_note_comment, weibo_creator |
| 贴吧 | tieba | tieba_note, tieba_note_comment, tieba_creator |
| 知乎 | zhihu | zhihu_note, zhihu_note_comment, zhihu_creator |

## 使用方法

### 1. 查看数据库状态

```bash
# 查看所有平台的数据统计
python3 quick_clear_all_data.py --status
```

输出示例：
```
📊 数据库状态统计:
------------------------------------------------------------

🎯 BILIBILI 平台:
  📋 bilibili_video: 1,234 条
  📋 bilibili_video_comment: 5,678 条
  📋 bilibili_creator: 56 条
  📊 平台小计: 6,968 条

🎯 XIAOHONGSHU 平台:
  📋 xhs_note: 空
  📋 xhs_note_comment: 空
  📋 xhs_creator: 空

============================================================
📊 数据库总计: 6,968 条记录
============================================================
```

### 2. 清理特定平台数据

```bash
# 清理B站数据
python3 quick_clear_all_data.py --platform bilibili

# 清理小红书数据
python3 quick_clear_all_data.py --platform xiaohongshu

# 清理抖音数据
python3 quick_clear_all_data.py --platform douyin
```

### 3. 清理所有平台数据

```bash
# 清理所有平台的所有数据
python3 quick_clear_all_data.py --all
```

### 4. 获取帮助信息

```bash
python3 quick_clear_all_data.py --help
```

## 清理顺序

工具会按照以下顺序清理数据，确保外键约束不会出现问题：

1. **评论数据** - 先清理评论表
2. **内容数据** - 再清理视频/笔记表
3. **创作者数据** - 最后清理创作者表

## 使用场景

### 场景1：定期清理测试数据
```bash
# 每次测试前清理所有数据
python3 quick_clear_all_data.py --all
```

### 场景2：清理特定平台数据
```bash
# 只清理B站相关数据
python3 quick_clear_all_data.py --platform bilibili
```

### 场景3：数据库维护
```bash
# 先查看状态
python3 quick_clear_all_data.py --status

# 根据需要清理特定平台
python3 quick_clear_all_data.py --platform xiaohongshu
```

### 场景4：批量处理前的准备
```bash
# 在开始大批量爬取前清空数据库
python3 quick_clear_all_data.py --all
```

## 输出示例

### 清理单个平台
```
🧹 数据库清理工具
==================================================
[12:34:56] 正在连接数据库...
[12:34:56] ✅ 数据库连接成功
[12:34:56] ⚠️  即将清理 BILIBILI 平台数据!
[12:34:56] 🎯 开始清理 BILIBILI 平台数据...
[12:34:56] 🗑️  正在清空表 bilibili_video_comment (5,678 条数据)...
[12:34:57] ✅ 成功清空表 bilibili_video_comment (5,678 条数据)
[12:34:57] 🗑️  正在清空表 bilibili_video (1,234 条数据)...
[12:34:58] ✅ 成功清空表 bilibili_video (1,234 条数据)
[12:34:58] 🗑️  正在清空表 bilibili_creator (56 条数据)...
[12:34:58] ✅ 成功清空表 bilibili_creator (56 条数据)
[12:34:58] 📊 BILIBILI 平台清理完成: 共清理 6,968 条数据
[12:34:58] 🎉 BILIBILI 平台数据清理完成
[12:34:58] 数据库连接已关闭
```

### 清理所有平台
```
🧹 数据库清理工具
==================================================
[12:35:00] 正在连接数据库...
[12:35:00] ✅ 数据库连接成功
[12:35:00] ⚠️  即将清理所有平台数据!
[12:35:00] 🌐 开始清理所有平台数据...

==================================================
[12:35:00] 🎯 开始清理 BILIBILI 平台数据...
[12:35:00] 📋 表 bilibili_video_comment 已为空，跳过
[12:35:00] 📋 表 bilibili_video 已为空，跳过
[12:35:00] 📋 表 bilibili_creator 已为空，跳过
[12:35:00] 📊 BILIBILI 平台清理完成: 共清理 0 条数据

==================================================
[12:35:00] 🎯 开始清理 XIAOHONGSHU 平台数据...
[12:35:00] 📋 表 xhs_note_comment 已为空，跳过
[12:35:00] 📋 表 xhs_note 已为空，跳过
[12:35:00] 📋 表 xhs_creator 已为空，跳过
[12:35:00] 📊 XIAOHONGSHU 平台清理完成: 共清理 0 条数据

==================================================
[12:35:01] 🎉 所有平台清理完成!
[12:35:01] 📊 成功清理 7/7 个平台
[12:35:01] 数据库连接已关闭
```

## 安全注意事项

⚠️ **重要警告**：
1. **数据不可恢复**: 清理操作会永久删除数据，无法恢复
2. **生产环境**: 在生产环境使用前请务必备份数据库
3. **权限检查**: 确保数据库用户有DELETE权限
4. **网络连接**: 确保数据库连接稳定

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   ❌ 导入模块失败: No module named 'aiomysql'
   ```
   解决方案：安装依赖 `pip install aiomysql`

2. **权限不足**
   ```
   ❌ 清空表 bilibili_video 失败: Access denied
   ```
   解决方案：检查数据库用户权限

3. **表不存在**
   ```
   ⚠️  获取表 bilibili_video 数量失败: Table doesn't exist
   ```
   解决方案：检查数据库表结构是否正确

4. **路径问题**
   ```
   ❌ 无法找到MediaCrawlerPro-Python目录
   ```
   解决方案：确保脚本在正确位置运行

### 调试模式

如果遇到问题，可以先使用 `--status` 参数检查数据库状态：

```bash
python3 quick_clear_all_data.py --status
```

## 与其他工具的关系

- **quick_clear_bili_comments.py**: 只清理B站评论的简化版本
- **quick_clear_all_data.py**: 全功能版本，支持所有平台和数据类型
- **keyword_auto_crawler.py**: 关键词自动爬虫，可配合使用

## 最佳实践

1. **测试前清理**: 每次测试前使用 `--all` 清理所有数据
2. **定期维护**: 定期使用 `--status` 查看数据库状态
3. **分平台管理**: 根据需要使用 `--platform` 清理特定平台
4. **备份习惯**: 重要数据清理前先备份
5. **日志监控**: 关注清理过程中的错误信息

## 更新日志

- v1.0.0: 初始版本，支持多平台数据清理
- 支持状态查看、单平台清理、全平台清理
- 添加详细的日志记录和错误处理 