#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP中间件 - 支持通过HTTP POST方式执行爬取任务
"""

import asyncio
import json
import os
import sys
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional
import uuid
import logging
from collections import defaultdict, deque

from flask import Flask, request, jsonify
from werkzeug.serving import make_server

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
# 延迟导入main模块，避免初始化时的配置问题
# from main import main as crawler_main
from cmd_arg import arg as cmd_arg


class APIRateLimiter:
    """API频率限制器"""

    def __init__(self):
        # 频率限制配置
        self.min_interval = 10  # 最小间隔10秒
        self.max_requests_per_minute = 3  # 1分钟最多3次请求
        self.debounce_delay = 5  # 连续请求去重延迟5秒

        # 请求记录
        self.request_times = deque()  # 记录请求时间
        self.last_request_time = 0  # 最后一次请求时间
        self.pending_requests = {}  # 待处理的请求
        self.request_timers = {}  # 请求定时器

        self.logger = logging.getLogger('api_rate_limiter')

    def is_rate_limited(self) -> tuple[bool, str]:
        """检查是否被频率限制"""
        current_time = time.time()

        # 清理1分钟前的请求记录
        while self.request_times and current_time - self.request_times[0] > 60:
            self.request_times.popleft()

        # 检查10秒间隔限制
        if current_time - self.last_request_time < self.min_interval:
            remaining = self.min_interval - (current_time - self.last_request_time)
            return True, f"请求过于频繁，请等待 {remaining:.1f} 秒后重试"

        # 检查1分钟内请求次数限制
        if len(self.request_times) >= self.max_requests_per_minute:
            oldest_request = self.request_times[0]
            remaining = 60 - (current_time - oldest_request)
            return True, f"1分钟内请求次数过多，请等待 {remaining:.1f} 秒后重试"

        return False, ""

    def add_request_record(self):
        """添加请求记录"""
        current_time = time.time()
        self.request_times.append(current_time)
        self.last_request_time = current_time

    def should_debounce_request(self, request_data: dict) -> tuple[bool, str]:
        """检查是否应该对请求进行去重处理"""
        current_time = time.time()
        request_key = self._generate_request_key(request_data)

        # 如果有相同的待处理请求，取消之前的定时器
        if request_key in self.request_timers:
            old_timer = self.request_timers[request_key]
            if old_timer.is_alive():
                old_timer.cancel()
                self.logger.info(f"取消之前的请求定时器: {request_key}")

        # 存储当前请求
        self.pending_requests[request_key] = {
            'data': request_data,
            'timestamp': current_time
        }

        return True, request_key

    def _generate_request_key(self, request_data: dict) -> str:
        """生成请求的唯一标识"""
        # 基于请求的关键参数生成key
        key_parts = [
            request_data.get('platform', ''),
            request_data.get('type', ''),
            str(request_data.get('bv_list', [])),
            request_data.get('keywords', ''),
            str(request_data.get('max_videos', '')),
            str(request_data.get('enable_comments', '')),
            str(request_data.get('enable_sub_comments', ''))
        ]
        return '|'.join(key_parts)

    def schedule_delayed_execution(self, request_key: str, callback, *args, **kwargs):
        """安排延迟执行"""
        def delayed_execution():
            time.sleep(self.debounce_delay)
            if request_key in self.pending_requests:
                self.logger.info(f"执行延迟请求: {request_key}")
                # 清理记录
                del self.pending_requests[request_key]
                if request_key in self.request_timers:
                    del self.request_timers[request_key]
                # 执行回调
                callback(*args, **kwargs)
            else:
                self.logger.info(f"请求已被新请求覆盖，跳过执行: {request_key}")

        # 创建并启动定时器
        timer = threading.Timer(self.debounce_delay, delayed_execution)
        self.request_timers[request_key] = timer
        timer.start()

        self.logger.info(f"安排延迟执行 ({self.debounce_delay}秒): {request_key}")


class CrawlerTaskManager:
    """爬虫任务管理器"""

    def __init__(self):
        self.tasks = {}  # 存储任务状态
        self.rate_limiter = APIRateLimiter()  # API频率限制器
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('http_middleware')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def create_task(self, task_config: Dict[str, Any]) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        
        self.tasks[task_id] = {
            'id': task_id,
            'status': 'pending',  # pending, running, completed, failed
            'config': task_config,
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'error': None,
            'progress': {
                'total': 0,
                'processed': 0,
                'failed': 0,
                'success': 0
            }
        }
        
        self.logger.info(f"创建任务: {task_id}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def update_task_status(self, task_id: str, status: str, error: str = None):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id]['status'] = status
            if status == 'running':
                self.tasks[task_id]['started_at'] = datetime.now().isoformat()
            elif status in ['completed', 'failed']:
                self.tasks[task_id]['completed_at'] = datetime.now().isoformat()
            if error:
                self.tasks[task_id]['error'] = error
    
    async def execute_task(self, task_id: str):
        """执行爬虫任务"""
        task = self.tasks.get(task_id)
        if not task:
            return

        original_config = None
        try:
            self.update_task_status(task_id, 'running')
            self.logger.info(f"开始执行任务: {task_id}")

            # 备份原始配置
            original_config = self._backup_config()

            # 设置任务配置
            self._apply_task_config(task['config'])

            # 动态导入并执行爬虫主程序
            from main import main as crawler_main
            await crawler_main()

            self.update_task_status(task_id, 'completed')
            self.logger.info(f"任务执行完成: {task_id}")

        except Exception as e:
            error_msg = str(e)
            self.update_task_status(task_id, 'failed', error_msg)
            self.logger.error(f"任务执行失败: {task_id}, 错误: {error_msg}")
        finally:
            # 恢复原始配置
            if original_config:
                self._restore_config(original_config)

            # 清理临时文件
            self._cleanup_temp_files()

            # 确保清理资源，避免asyncio连接错误
            try:
                import gc
                gc.collect()
                # 给一点时间让连接正确关闭
                await asyncio.sleep(0.1)
            except Exception as cleanup_error:
                self.logger.warning(f"清理资源时出现警告: {cleanup_error}")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        temp_files = getattr(self, 'temp_files', [])
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    self.logger.info(f"清理临时文件: {temp_file}")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败 {temp_file}: {e}")
        self.temp_files = []
    
    def _backup_config(self) -> Dict[str, Any]:
        """备份当前配置"""
        return {
            'PLATFORM': getattr(config, 'PLATFORM', None),
            'CRAWLER_TYPE': getattr(config, 'CRAWLER_TYPE', None),
            'BILI_BV_JSON': getattr(config, 'BILI_BV_JSON', None),
            'BILI_BV_TXT': getattr(config, 'BILI_BV_TXT', None),
            'BILI_BV_COMPLEX_JSON': getattr(config, 'BILI_BV_COMPLEX_JSON', None),
            'BILI_UID_JSON': getattr(config, 'BILI_UID_JSON', None),
            'KEYWORDS': getattr(config, 'KEYWORDS', None),
            # 备份爬取参数配置
            'CRAWLER_MAX_NOTES_COUNT': getattr(config, 'CRAWLER_MAX_NOTES_COUNT', None),
            'ENABLE_GET_COMMENTS': getattr(config, 'ENABLE_GET_COMMENTS', None),
            'ENABLE_GET_SUB_COMMENTS': getattr(config, 'ENABLE_GET_SUB_COMMENTS', None),
            'PER_NOTE_MAX_COMMENTS_COUNT': getattr(config, 'PER_NOTE_MAX_COMMENTS_COUNT', None),
        }
    
    def _restore_config(self, original_config: Dict[str, Any]):
        """恢复原始配置"""
        for key, value in original_config.items():
            setattr(config, key, value)
    
    def _apply_task_config(self, task_config: Dict[str, Any]):
        """应用任务配置"""
        self.logger.info(f"应用任务配置: {task_config}")

        # 清空所有相关配置，避免冲突
        config.BILI_BV_JSON = None
        config.BILI_BV_TXT = None
        config.BILI_BV_COMPLEX_JSON = None
        config.BILI_UID_JSON = None
        config.KEYWORDS = None
        config.BILI_SPECIFIED_ID_LIST = None

        # 设置平台
        config.PLATFORM = task_config.get('platform', 'bili')

        # 设置爬虫类型
        config.CRAWLER_TYPE = task_config.get('type', 'detail')

        # 处理直接传递的BV号数组
        if 'bv_list' in task_config:
            bv_list = task_config['bv_list']
            if isinstance(bv_list, list) and bv_list:
                # 创建临时JSON文件
                import tempfile
                import json
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                json.dump(bv_list, temp_file, ensure_ascii=False, indent=2)
                temp_file.close()

                config.BILI_BV_JSON = temp_file.name
                self.logger.info(f"设置 BILI_BV_JSON (临时文件): {config.BILI_BV_JSON}")
                self.logger.info(f"BV号数量: {len(bv_list)}")

                # 记录临时文件，用于后续清理
                self.temp_files = getattr(self, 'temp_files', [])
                self.temp_files.append(temp_file.name)

        # 处理复杂JSON格式的BV号数组
        elif 'bv_complex_data' in task_config:
            complex_data = task_config['bv_complex_data']
            if complex_data:
                # 创建临时JSON文件
                import tempfile
                import json
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                json.dump(complex_data, temp_file, ensure_ascii=False, indent=2)
                temp_file.close()

                config.BILI_BV_COMPLEX_JSON = temp_file.name
                self.logger.info(f"设置 BILI_BV_COMPLEX_JSON (临时文件): {config.BILI_BV_COMPLEX_JSON}")

                # 记录临时文件，用于后续清理
                self.temp_files = getattr(self, 'temp_files', [])
                self.temp_files.append(temp_file.name)

        # 设置文件路径
        elif 'bv_json_file' in task_config:
            config.BILI_BV_JSON = task_config['bv_json_file']
            self.logger.info(f"设置 BILI_BV_JSON: {config.BILI_BV_JSON}")

        elif 'bv_txt_file' in task_config:
            config.BILI_BV_TXT = task_config['bv_txt_file']
            self.logger.info(f"设置 BILI_BV_TXT: {config.BILI_BV_TXT}")

        elif 'bv_complex_json_file' in task_config:
            config.BILI_BV_COMPLEX_JSON = task_config['bv_complex_json_file']
            self.logger.info(f"设置 BILI_BV_COMPLEX_JSON: {config.BILI_BV_COMPLEX_JSON}")

        elif 'uid_json_file' in task_config:
            config.BILI_UID_JSON = task_config['uid_json_file']
            self.logger.info(f"设置 BILI_UID_JSON: {config.BILI_UID_JSON}")

        elif 'keywords' in task_config:
            config.KEYWORDS = task_config['keywords']
            self.logger.info(f"设置 KEYWORDS: {config.KEYWORDS}")

        # 设置爬取参数配置
        if 'max_videos' in task_config:
            config.CRAWLER_MAX_NOTES_COUNT = int(task_config['max_videos'])
            self.logger.info(f"设置 CRAWLER_MAX_NOTES_COUNT: {config.CRAWLER_MAX_NOTES_COUNT}")

        if 'enable_comments' in task_config:
            config.ENABLE_GET_COMMENTS = bool(task_config['enable_comments'])
            self.logger.info(f"设置 ENABLE_GET_COMMENTS: {config.ENABLE_GET_COMMENTS}")

        if 'enable_sub_comments' in task_config:
            config.ENABLE_GET_SUB_COMMENTS = bool(task_config['enable_sub_comments'])
            self.logger.info(f"设置 ENABLE_GET_SUB_COMMENTS: {config.ENABLE_GET_SUB_COMMENTS}")

        if 'max_comments_per_video' in task_config:
            config.PER_NOTE_MAX_COMMENTS_COUNT = int(task_config['max_comments_per_video'])
            self.logger.info(f"设置 PER_NOTE_MAX_COMMENTS_COUNT: {config.PER_NOTE_MAX_COMMENTS_COUNT}")

        self.logger.info(f"配置应用完成 - PLATFORM: {config.PLATFORM}, TYPE: {config.CRAWLER_TYPE}")
        self.logger.info(f"爬取参数 - 最大视频数: {getattr(config, 'CRAWLER_MAX_NOTES_COUNT', '默认')}, "
                        f"爬取评论: {getattr(config, 'ENABLE_GET_COMMENTS', '默认')}, "
                        f"爬取二级评论: {getattr(config, 'ENABLE_GET_SUB_COMMENTS', '默认')}, "
                        f"每视频最大评论数: {getattr(config, 'PER_NOTE_MAX_COMMENTS_COUNT', '默认')}")


# 创建Flask应用和任务管理器
app = Flask(__name__)
task_manager = CrawlerTaskManager()


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'service': 'MediaCrawlerPro HTTP Middleware'
    })


@app.route('/tasks', methods=['POST'])
def create_crawl_task():
    """创建爬取任务"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求体不能为空'}), 400

        # API频率限制检查
        is_limited, limit_message = task_manager.rate_limiter.is_rate_limited()
        if is_limited:
            return jsonify({'error': limit_message}), 429

        # 验证必需参数
        platform = data.get('platform', 'bili')
        task_type = data.get('type', 'detail')
        
        # 验证参数
        file_path = None
        has_valid_input = False

        # 检查直接传递的BV号数组
        if 'bv_list' in data:
            bv_list = data['bv_list']
            if isinstance(bv_list, list) and bv_list:
                has_valid_input = True
                # 验证BV号格式
                for bv in bv_list:
                    if not isinstance(bv, str) or not bv.startswith('BV'):
                        return jsonify({'error': f'无效的BV号格式: {bv}'}), 400
            else:
                return jsonify({'error': 'bv_list必须是非空数组'}), 400

        # 检查复杂JSON数据
        elif 'bv_complex_data' in data:
            complex_data = data['bv_complex_data']
            if complex_data:
                has_valid_input = True
            else:
                return jsonify({'error': 'bv_complex_data不能为空'}), 400

        # 检查文件路径
        elif 'bv_json_file' in data:
            file_path = data['bv_json_file']
            has_valid_input = True
        elif 'bv_txt_file' in data:
            file_path = data['bv_txt_file']
            has_valid_input = True
        elif 'bv_complex_json_file' in data:
            file_path = data['bv_complex_json_file']
            has_valid_input = True
        elif 'uid_json_file' in data:
            file_path = data['uid_json_file']
            has_valid_input = True
        elif task_type == 'search' and 'keywords' in data:
            # 搜索任务不需要文件
            has_valid_input = True

        if not has_valid_input:
            return jsonify({'error': '必须指定BV号数组、复杂JSON数据、文件路径或关键词'}), 400

        # 检查文件是否存在
        if file_path and not os.path.exists(file_path):
            return jsonify({'error': f'文件不存在: {file_path}'}), 400
        
        # 检查是否需要去重处理
        should_debounce, request_key = task_manager.rate_limiter.should_debounce_request(data)

        if should_debounce:
            # 创建任务但延迟执行
            task_id = task_manager.create_task(data)

            # 定义延迟执行函数
            def delayed_run_task():
                # 记录请求
                task_manager.rate_limiter.add_request_record()

                # 异步执行任务
                def run_task():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(task_manager.execute_task(task_id))
                    except Exception as e:
                        task_manager.logger.error(f"任务线程执行错误: {e}")
                    finally:
                        try:
                            # 确保所有任务完成
                            pending = asyncio.all_tasks(loop)
                            if pending:
                                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                            # 关闭事件循环
                            loop.close()
                        except Exception as e:
                            task_manager.logger.warning(f"关闭事件循环时出现警告: {e}")

                thread = threading.Thread(target=run_task)
                thread.daemon = True
                thread.start()

            # 安排延迟执行
            task_manager.rate_limiter.schedule_delayed_execution(request_key, delayed_run_task)

            return jsonify({
                'task_id': task_id,
                'status': 'scheduled',
                'message': f'任务已安排，将在 {task_manager.rate_limiter.debounce_delay} 秒后执行（如无新的相同请求）',
                'debounce_delay': task_manager.rate_limiter.debounce_delay
            }), 202
        else:
            # 立即执行（不应该到达这里，因为上面已经检查了频率限制）
            task_manager.rate_limiter.add_request_record()
            task_id = task_manager.create_task(data)

            def run_task():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(task_manager.execute_task(task_id))
                except Exception as e:
                    task_manager.logger.error(f"任务线程执行错误: {e}")
                finally:
                    try:
                        pending = asyncio.all_tasks(loop)
                        if pending:
                            loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        loop.close()
                    except Exception as e:
                        task_manager.logger.warning(f"关闭事件循环时出现警告: {e}")

            thread = threading.Thread(target=run_task)
            thread.daemon = True
            thread.start()

            return jsonify({
                'task_id': task_id,
                'status': 'created',
                'message': '任务已创建并开始执行'
            }), 201
        
    except Exception as e:
        return jsonify({'error': f'创建任务失败: {str(e)}'}), 500


@app.route('/tasks/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态"""
    task = task_manager.get_task_status(task_id)

    if not task:
        return jsonify({'error': '任务不存在'}), 404

    return jsonify(task)


@app.route('/rate-limit/status', methods=['GET'])
def get_rate_limit_status():
    """获取API限制状态"""
    current_time = time.time()
    rate_limiter = task_manager.rate_limiter

    # 清理过期的请求记录
    while rate_limiter.request_times and current_time - rate_limiter.request_times[0] > 60:
        rate_limiter.request_times.popleft()

    # 计算下次可请求时间
    next_request_time = rate_limiter.last_request_time + rate_limiter.min_interval
    time_until_next = max(0, next_request_time - current_time)

    return jsonify({
        'current_time': current_time,
        'last_request_time': rate_limiter.last_request_time,
        'requests_in_last_minute': len(rate_limiter.request_times),
        'max_requests_per_minute': rate_limiter.max_requests_per_minute,
        'min_interval_seconds': rate_limiter.min_interval,
        'time_until_next_request': time_until_next,
        'pending_requests_count': len(rate_limiter.pending_requests),
        'active_timers_count': len(rate_limiter.request_timers),
        'debounce_delay_seconds': rate_limiter.debounce_delay
    })


@app.route('/tasks', methods=['GET'])
def list_tasks():
    """列出所有任务"""
    tasks = list(task_manager.tasks.values())
    return jsonify({
        'tasks': tasks,
        'total': len(tasks)
    })


@app.route('/tasks/<task_id>', methods=['DELETE'])
def cancel_task(task_id):
    """取消任务（仅限pending状态）"""
    task = task_manager.get_task_status(task_id)
    
    if not task:
        return jsonify({'error': '任务不存在'}), 404
    
    if task['status'] == 'pending':
        task_manager.update_task_status(task_id, 'cancelled')
        return jsonify({'message': '任务已取消'})
    else:
        return jsonify({'error': f'无法取消状态为 {task["status"]} 的任务'}), 400


def run_server(host='0.0.0.0', port=8080, debug=False):
    """运行HTTP服务器"""
    print(f"""
🚀 MediaCrawlerPro HTTP中间件启动成功！

服务地址: http://{host}:{port}
健康检查: http://{host}:{port}/health

API文档:
- POST /tasks - 创建爬取任务
- GET /tasks/<task_id> - 获取任务状态  
- GET /tasks - 列出所有任务
- DELETE /tasks/<task_id> - 取消任务

示例请求:
# 使用BV号数组
curl -X POST http://{host}:{port}/tasks \\
  -H "Content-Type: application/json" \\
  -d '{{"platform": "bili", "type": "detail", "bv_list": ["BV1aRKHz2Ejd", "BV1HzzGY3ExC"]}}'

# 使用文件路径
curl -X POST http://{host}:{port}/tasks \\
  -H "Content-Type: application/json" \\
  -d '{{"platform": "bili", "type": "detail", "bv_complex_json_file": "/path/to/bv.json"}}'
""")
    
    if debug:
        app.run(host=host, port=port, debug=True)
    else:
        server = make_server(host, port, app)
        server.serve_forever()


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='MediaCrawlerPro HTTP中间件')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址')
    parser.add_argument('--port', type=int, default=8080, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    run_server(host=args.host, port=args.port, debug=args.debug)
