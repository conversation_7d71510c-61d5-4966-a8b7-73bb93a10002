# MediaCrawlerPro HTTP中间件

## 概述

HTTP中间件为MediaCrawlerPro提供了RESTful API接口，支持通过HTTP POST方式执行指定JSON文件的数据爬取任务。该中间件以最小改动的方式集成到现有程序中，提供了完整的任务管理功能。

## 功能特性

- 🚀 **RESTful API**: 提供标准的HTTP API接口
- 📝 **任务管理**: 支持创建、查询、监控爬取任务
- 🔄 **异步执行**: 任务在后台异步执行，不阻塞API响应
- 📊 **状态监控**: 实时查看任务执行状态和进度
- 🌐 **Web界面**: 提供友好的Web控制台
- 📁 **多格式支持**: 支持复杂JSON、简单JSON、TXT等多种文件格式

## 快速开始

### 1. 安装依赖

```bash
# 自动安装依赖
python3 install_http_dependencies.py

# 或手动安装
pip install flask requests
```

### 2. 启动HTTP服务

```bash
# 方式1: 使用启动脚本（推荐）
python3 start_http_server.py

# 方式2: 直接启动
python3 http_middleware.py

# 方式3: 自定义配置
python3 http_middleware.py --host 0.0.0.0 --port 8080 --debug
```

### 3. 验证服务

```bash
# 健康检查
curl http://localhost:8080/health
```

## API文档

### 基础信息

- **服务地址**: `http://localhost:8080`
- **Content-Type**: `application/json`

### 端点列表

#### 1. 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00",
  "service": "MediaCrawlerPro HTTP Middleware"
}
```

#### 2. 创建任务
```http
POST /tasks
```

**请求体格式**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_json_file": "/path/to/bv.json"
}
```

**支持的任务类型**:

1. **直接传递BV号数组** (推荐):
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_list": ["BV1aRKHz2Ejd", "BV1HzzGY3ExC", "BV1xyz123abc"]
}
```

2. **直接传递复杂JSON数据**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_data": [
    {
      "data": {
        "items": [
          {
            "fields": {
              "bv": [{"text": "BV1aRKHz2Ejd"}]
            }
          }
        ]
      }
    }
  ]
}
```

3. **复杂JSON格式BV号爬取**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_complex_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"
}
```

4. **简单JSON格式BV号爬取**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_json_file": "/path/to/bv_list.json"
}
```

3. **TXT文件BV号爬取**:
```json
{
  "platform": "bili",
  "type": "detail",
  "bv_txt_file": "/path/to/bv_list.txt"
}
```

4. **关键词搜索**:
```json
{
  "platform": "bili",
  "type": "search",
  "keywords": "deepseek,chatgpt"
}
```

5. **UP主数据爬取**:
```json
{
  "platform": "bili",
  "type": "creator",
  "uid_json_file": "/path/to/uid_list.json"
}
```

**响应示例**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "created",
  "message": "任务已创建并开始执行"
}
```

#### 3. 获取任务状态
```http
GET /tasks/{task_id}
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "status": "running",
  "config": {
    "platform": "bili",
    "type": "detail",
    "bv_complex_json_file": "/path/to/bv.json"
  },
  "created_at": "2024-01-01T12:00:00",
  "started_at": "2024-01-01T12:00:01",
  "completed_at": null,
  "error": null,
  "progress": {
    "total": 100,
    "processed": 50,
    "failed": 2,
    "success": 48
  }
}
```

**任务状态说明**:
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 执行完成
- `failed`: 执行失败
- `cancelled`: 已取消

#### 4. 列出所有任务
```http
GET /tasks
```

**响应示例**:
```json
{
  "tasks": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "status": "completed",
      "config": {...},
      "created_at": "2024-01-01T12:00:00"
    }
  ],
  "total": 1
}
```

#### 5. 取消任务
```http
DELETE /tasks/{task_id}
```

**响应示例**:
```json
{
  "message": "任务已取消"
}
```

## 使用示例

### 1. 命令行示例

```bash
# 创建复杂JSON格式任务
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_complex_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"}'

# 获取任务状态
curl http://localhost:8080/tasks/TASK_ID

# 列出所有任务
curl http://localhost:8080/tasks
```

### 2. Python示例

```python
import requests
import json

# 创建任务
task_data = {
    "platform": "bili",
    "type": "detail",
    "bv_complex_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"
}

response = requests.post(
    "http://localhost:8080/tasks",
    headers={"Content-Type": "application/json"},
    data=json.dumps(task_data)
)

result = response.json()
task_id = result['task_id']

# 监控任务状态
import time
while True:
    status_response = requests.get(f"http://localhost:8080/tasks/{task_id}")
    status = status_response.json()['status']
    
    if status in ['completed', 'failed']:
        break
    
    time.sleep(10)
```

### 3. Web界面

1. 启动HTTP服务
2. 在浏览器中打开 `web_interface.html`
3. 通过Web界面创建和监控任务

## 文件格式支持

### 复杂JSON格式 (推荐)
支持您的 `bv.json` 文件格式:
```json
[
  {
    "code": 0,
    "data": {
      "items": [
        {
          "fields": {
            "bv": [
              {
                "text": "BV1aRKHz2Ejd",
                "type": "text"
              }
            ]
          }
        }
      ]
    }
  }
]
```

### 简单JSON格式
```json
["BV1HzzGY3ExC", "BV1T5q5YnEW4", "BV1PS6PYtEpi"]
```

### TXT格式
```
BV1HzzGY3ExC
https://www.bilibili.com/video/BV1T5q5YnEW4/
BV1PS6PYtEpi
```

## 故障排除

### 1. 服务启动失败
- 检查端口是否被占用
- 确保Python环境正确
- 检查依赖是否安装完整

### 2. 任务创建失败
- 检查文件路径是否正确
- 确保文件格式符合要求
- 查看错误日志

### 3. 任务执行失败
- 检查数据库连接
- 确保Redis服务正常
- 查看任务错误信息

## 高级配置

### 自定义端口和地址
```bash
python3 http_middleware.py --host 0.0.0.0 --port 9000
```

### 调试模式
```bash
python3 http_middleware.py --debug
```

### 生产环境部署
建议使用 Gunicorn 或 uWSGI 部署:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8080 http_middleware:app
```

## 注意事项

1. **文件路径**: 确保指定的文件路径存在且可读
2. **并发限制**: 同时运行的任务数量受系统资源限制
3. **数据库**: 确保MySQL和Redis服务正常运行
4. **权限**: 确保程序有足够的文件读写权限
5. **网络**: 确保网络连接正常，代理配置正确

## 更多信息

- 查看 `http_api_examples.py` 了解更多API使用示例
- 使用 `web_interface.html` 体验Web控制台
- 参考主程序文档了解爬虫配置选项
