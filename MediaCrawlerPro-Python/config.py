# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# 1. 不得用于任何商业用途。  
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# 3. 不得进行大规模爬取或对平台造成运营干扰。  
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# 5. 不得用于任何非法或不当的用途。
#   
# 详细许可条款请参阅项目根目录下的LICENSE文件。  
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。  

# 主程序配置文件 - 整合所有配置模块
import os
from typing import List

# 导入所有配置模块的内容
try:
    from MediaCrawlerPro-Python.config.base_config import *
    from MediaCrawlerPro-Python.config.db_config import *
    from MediaCrawlerPro-Python.config.proxy_config import *
    from MediaCrawlerPro-Python.config.sign_srv_config import *
except ImportError:
    # 如果无法导入config包，则直接定义必要的配置
    try:
        from constant import MYSQL_ACCOUNT_SAVE
    except ImportError:
        MYSQL_ACCOUNT_SAVE = "mysql"
    
    # 基础配置
    PLATFORM = "bili"
    KEYWORDS = "deepseek,chatgpt"
    SORT_TYPE = "popularity_descending"
    PUBLISH_TIME_TYPE = 0
    CRAWLER_TYPE = "detail"
    SAVE_DATA_OPTION = "db"
    ACCOUNT_POOL_SAVE_TYPE = os.getenv("ACCOUNT_POOL_SAVE_TYPE", MYSQL_ACCOUNT_SAVE)
    START_PAGE = 1
    CRAWLER_MAX_NOTES_COUNT = 120
    MAX_CONCURRENCY_NUM = 3
    ENABLE_GET_COMMENTS = True
    ENABLE_GET_SUB_COMMENTS = True
    PER_NOTE_MAX_COMMENTS_COUNT = 0
    ENABLE_LOG_FILE = True
    
    # 批量爬取配置
    PROGRESS_FILE_PATH = "crawler_progress.json"
    BATCH_CRAWLER_LOG_FILE = "batch_crawler.log"
    ENABLE_BATCH_LOG = True
    MAX_RETRY_COUNT = 0
    RETRY_DELAY = 60
    ACCOUNT_PROCESS_DELAY_MIN = 2
    ACCOUNT_PROCESS_DELAY_MAX = 5
    VIDEO_PROCESS_DELAY_MIN = 0
    VIDEO_PROCESS_DELAY_MAX = 1
    
    # 冷却时间配置
    ENABLE_COOLDOWN = True
    COOLDOWN_TRIGGER_COUNT = 100
    COOLDOWN_DURATION = 60
    COOLDOWN_DISPLAY_INTERVAL = 10
    
    # 批量爬取文件路径配置
    BILI_UID_JSON = None
    BILI_BV_JSON = None
    BILI_BV_TXT = None
    BILI_BV_COMPLEX_JSON = None
    
    # 数据库配置
    MYSQL_DB_HOST = os.getenv("MYSQL_DB_HOST", "mysql_db")
    MYSQL_DB_PORT = int(os.getenv("MYSQL_DB_PORT", 3306))
    MYSQL_DB_PWD = os.getenv("MYSQL_DB_PWD", "123456")
    MYSQL_DB_USER = os.getenv("MYSQL_DB_USER", "root")
    MYSQL_DB_NAME = os.getenv("MYSQL_DB_NAME", "media_crawler")
    
    # 是否开启 IP 代理
ENABLE_IP_PROXY = True

# 代理IP池数量
IP_PROXY_POOL_COUNT = 2  # 一般情况下设置成2个就够了，程序会自动维护IP可用性

# 代理IP提供商名称
IP_PROXY_PROVIDER_NAME = "kuaidaili"

# 快代理配置
KDL_SECERT_ID = os.getenv("KDL_SECERT_ID", "ouxa3g1cksa1dtxfmt94")
KDL_SIGNATURE = os.getenv("KDL_SIGNATURE", "1nwhr0uwf0bxsvlg274m35gpnxo5m18k")
KDL_USER_NAME = os.getenv("KDL_USER_NAME", "d4676964737")
KDL_USER_PWD = os.getenv("KDL_USER_PWD", "0p717tcc")

    
    # 签名服务配置
    SIGN_SRV_HOST = os.getenv("SIGN_SRV_HOST", "mediacrawler_signsrv")
    SIGN_SRV_PORT = int(os.getenv("SIGN_SRV_PORT", 8989))
    
    # 邮件配置
    ENABLE_EMAIL_NOTIFICATION = False
    EMAIL_SMTP_SERVER = "smtp.gmail.com"
    EMAIL_SMTP_PORT = 587
    EMAIL_SENDER = ""
    EMAIL_PASSWORD = ""
    EMAIL_RECEIVERS = []
    
    # 指定小红书需要爬虫的笔记URL列表
    XHS_SPECIFIED_NOTE_URL_LIST = []
    
    # 指定小红书创作者主页url列表
    XHS_CREATOR_URL_LIST = []
    
    # 指定微博平台需要爬取的帖子列表
    WEIBO_SPECIFIED_ID_LIST = []
    
    # 指定weibo创作者ID列表
    WEIBO_CREATOR_ID_LIST = []
    
    # 指定贴吧需要爬取的帖子列表
    TIEBA_SPECIFIED_ID_LIST: List[str] = []
    
    # 指定贴吧名称列表，爬取该贴吧下的帖子
    TIEBA_NAME_LIST: List[str] = []
    
    TIEBA_CREATOR_URL_LIST = []
    
    # 指定bili创作者ID列表(这里是up主页面的ID)
    BILI_CREATOR_ID_LIST = []
    
    # 指定B站平台需要爬取的视频bvid列表
    BILI_SPECIFIED_ID_LIST = []
    
    # 指定抖音需要爬取的ID列表
    DY_SPECIFIED_ID_LIST = []
    
    # 指定Dy创作者ID列表(sec_id)
    DY_CREATOR_ID_LIST = []
    
    # 指定快手平台需要爬取的ID列表
    KS_SPECIFIED_ID_LIST = []
    
    # 指定快手创作者ID列表
    KS_CREATOR_ID_LIST = []
    
    # 指定知乎创作者主页url列表
    ZHIHU_CREATOR_URL_LIST = []
    
    # 指定知乎需要爬取的帖子ID列表
    ZHIHU_SPECIFIED_ID_LIST = []
