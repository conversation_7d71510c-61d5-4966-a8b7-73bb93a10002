# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。


# -*- coding: utf-8 -*-
import os

# ==================== 邮件通知配置 ====================

# 是否启用邮件通知
ENABLE_EMAIL_NOTIFICATION = True

# ==================== SMTP邮箱配置 ====================

# 发送方邮箱配置（建议使用QQ邮箱或163邮箱）
SENDER_EMAIL = os.getenv("SENDER_EMAIL", "<EMAIL>")  # 发送方邮箱
SENDER_PASSWORD = os.getenv("SENDER_PASSWORD", "orldxpapydpwgffj")  # 邮箱应用密码（不是登录密码）
# 接收方邮箱（可以是多个，用逗号分隔）
RECEIVER_EMAILS = os.getenv("RECEIVER_EMAILS", "<EMAIL>").split(",")

# ==================== SMTP服务器配置 ====================

# 常用SMTP服务器配置
SMTP_CONFIGS = {
    "qq.com": {
        "host": "smtp.qq.com",
        "port": 587,  # 改为587端口作为主配置
        "starttls": True,  # 使用TLS
        "ssl": False,  # 不使用SSL
        "timeout": 30,  # 增加超时时间
        # Windows系统备用配置
        "backup_configs": [
            {"host": "smtp.qq.com", "port": 465, "starttls": False, "ssl": True, "timeout": 30},  # 465作为备用
            {"host": "smtp.qq.com", "port": 25, "starttls": True, "ssl": False, "timeout": 30}
        ]
    },
    "163.com": {
        "host": "smtp.163.com", 
        "port": 25,
        "starttls": True,
        "timeout": 30,
        "backup_configs": [
            {"host": "smtp.163.com", "port": 465, "starttls": False, "ssl": True, "timeout": 30},
            {"host": "smtp.163.com", "port": 587, "starttls": True, "ssl": False, "timeout": 30}
        ]
    },
    "126.com": {
        "host": "smtp.126.com",
        "port": 25,
        "starttls": True,
        "timeout": 30,
        "backup_configs": [
            {"host": "smtp.126.com", "port": 465, "starttls": False, "ssl": True, "timeout": 30}
        ]
    },
    "gmail.com": {
        "host": "smtp.gmail.com",
        "port": 587,
        "starttls": True,
        "timeout": 30,
        "backup_configs": [
            {"host": "smtp.gmail.com", "port": 465, "starttls": False, "ssl": True, "timeout": 30}
        ]
    },
    "outlook.com": {
        "host": "smtp-mail.outlook.com",
        "port": 587,
        "starttls": True,
        "timeout": 30
    }
}

# 自动检测SMTP配置
def get_smtp_config(email: str) -> dict:
    """根据邮箱自动获取SMTP配置"""
    domain = email.split("@")[-1].lower()
    return SMTP_CONFIGS.get(domain, SMTP_CONFIGS["qq.com"])  # 默认使用QQ邮箱配置

# ==================== 邮件模板配置 ====================

# 邮件主题模板
EMAIL_SUBJECT_TEMPLATE = "🤖 MediaCrawlerPro 批量爬取任务完成通知 - {status}"

# 邮件内容模板（HTML格式）
EMAIL_BODY_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
        .content {{ background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }}
        .status-success {{ color: #28a745; font-weight: bold; }}
        .status-failed {{ color: #dc3545; font-weight: bold; }}
        .status-partial {{ color: #ffc107; font-weight: bold; }}
        .stats-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        .stats-table th, .stats-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .stats-table th {{ background-color: #f2f2f2; }}
        .failed-list {{ background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 10px 0; }}
        .info-box {{ background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 10px; margin: 10px 0; }}
        .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🤖 MediaCrawlerPro 任务完成通知</h2>
            <p>批量爬取任务已完成，以下是详细报告：</p>
        </div>
        <div class="content">
            <h3>📊 任务概览</h3>
            <table class="stats-table">
                <tr><th>项目</th><th>数值</th></tr>
                <tr><td>任务状态</td><td class="{status_class}">{overall_status}</td></tr>
                <tr><td>开始时间</td><td>{start_time}</td></tr>
                <tr><td>结束时间</td><td>{end_time}</td></tr>
                <tr><td>总耗时</td><td>{duration}</td></tr>
                <tr><td>总任务数</td><td>{total_count}</td></tr>
                <tr><td>成功数量</td><td style="color: #28a745;">{success_count}</td></tr>
                <tr><td>失败数量</td><td style="color: #dc3545;">{failed_count}</td></tr>
                <tr><td>跳过数量</td><td style="color: #6c757d;">{skipped_count}</td></tr>
                <tr><td>成功率</td><td>{success_rate}%</td></tr>
            </table>

            {failed_section}

            <div class="info-box">
                <h4>💡 后续操作建议</h4>
                {suggestions}
            </div>

            <h4>📂 文件信息</h4>
            <ul>
                <li><strong>进度文件状态:</strong> {progress_file_status}</li>
                <li><strong>日志文件:</strong> {log_file}</li>
                <li><strong>数据库:</strong> 已保存到MySQL数据库</li>
            </ul>
        </div>
        <div class="footer">
            <p>此邮件由 MediaCrawlerPro 自动发送 | {timestamp}</p>
        </div>
    </div>
</body>
</html>
"""

# 失败任务详情模板
FAILED_TASKS_TEMPLATE = """
<div class="failed-list">
    <h4>❌ 失败任务详情</h4>
    <ul>
    {failed_details}
    </ul>
</div>
""" 