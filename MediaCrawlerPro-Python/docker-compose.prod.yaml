# 生产环境 Docker Compose 配置文件
# 用于服务器部署，支持公网访问

services:
  app:
    build: .
    container_name: mediacrawlerpro
    ports:
      - "8080:8080"  # HTTP API服务 - 公网访问
    depends_on:
      - db
      - redis
      - signsrv
    volumes:
      - ./config:/app/config
      - ./crawler_progress.json:/app/crawler_progress.json
    environment:
      - RELATION_DB_USER=root
      - RELATION_DB_HOST=mysql_db
      - RELATION_DB_PWD=${MYSQL_ROOT_PASSWORD:-123456}
      - RELATION_DB_PORT=3306
      - RELATION_DB_NAME=media_crawler
      - REDIS_DB_HOST=redis_cache
      - REDIS_DB_PWD=${REDIS_PASSWORD:-123456}
      - REDIS_DB_PORT=6379
      - REDIS_DB_NUM=0
      - SIGN_SRV_HOST=mediacrawler_signsrv
      - SIGN_SRV_PORT=8989
    restart: unless-stopped
    networks:
      - mediacrawler_network

  signsrv:
    build: ../MediaCrawlerPro-SignSrv
    container_name: mediacrawler_signsrv
    # 签名服务不对外开放，仅内部访问
    environment:
      - APP_PORT=8989
      - APP_HOST=0.0.0.0
      - LOGGER_LEVEL=ERROR
    command: python app.py
    restart: unless-stopped
    networks:
      - mediacrawler_network

  db:
    image: mysql:8.0
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: media_crawler
      MYSQL_ROOT_HOST: '%'
    ports:
      - "${MYSQL_PUBLIC_PORT:-3307}:3306"  # MySQL数据库 - 可选择性对外开放
    volumes:
      - mysql_db_data:/var/lib/mysql
      - ./schema/tables.sql:/docker-entrypoint-initdb.d/01-tables.sql:ro
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0
    restart: unless-stopped
    networks:
      - mediacrawler_network

  redis:
    image: redis:7.0
    container_name: redis_cache
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-123456}
    # Redis不对外开放，仅内部访问
    volumes:
      - redis_data:/data
    command: [ "redis-server", "--requirepass", "${REDIS_PASSWORD:-123456}" ]
    restart: unless-stopped
    networks:
      - mediacrawler_network

networks:
  mediacrawler_network:
    driver: bridge

volumes:
  mysql_db_data:
  redis_data:
