# 📧 MediaCrawlerPro 邮件通知功能使用说明

## 📋 功能概述

MediaCrawlerPro 新增了邮件通知功能，当批量爬取任务完成后会自动发送详细的任务报告邮件，包括：

- ✅ 任务执行统计（成功/失败/跳过数量）
- ⏱️ 任务耗时和执行时间
- 📊 成功率统计
- ❌ 失败任务详情和错误信息
- 💡 后续操作建议
- 📂 进度文件状态

## 🔧 配置步骤

### 1. 编辑邮件配置

编辑文件：`config/email_config.py`

```python
# 发送方邮箱配置
SENDER_EMAIL = "<EMAIL>"  # 改为您的真实邮箱
SENDER_PASSWORD = "your_app_password"  # 改为您的邮箱应用密码

# 接收方邮箱（可配置多个）
RECEIVER_EMAILS = "<EMAIL>,<EMAIL>".split(",")
```

### 2. 获取邮箱应用密码

#### QQ邮箱
1. 登录 [QQ邮箱网页版](https://mail.qq.com)
2. 点击 **设置** → **账户**
3. 找到 **POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务**
4. 开启 **POP3/SMTP服务**
5. 发送短信验证，获取 **授权码**（这就是应用密码）

#### 163邮箱
1. 登录 [163邮箱网页版](https://mail.163.com)
2. 点击 **设置** → **POP3/SMTP/IMAP**
3. 开启 **客户端授权密码**
4. 设置授权密码（这就是应用密码）

#### Gmail邮箱
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用专用密码作为 SENDER_PASSWORD

### 3. 环境变量配置（推荐，更安全）

```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
export SENDER_EMAIL="<EMAIL>"
export SENDER_PASSWORD="your_app_password"
export RECEIVER_EMAILS="<EMAIL>,<EMAIL>"
```

## 🧪 测试配置

配置完成后，运行测试命令：

```bash
python3 test_email_config.py
```

如果配置正确，您将收到一封测试邮件。

## 📧 邮件模板预览

邮件包含以下内容：

### 邮件主题
```
🤖 MediaCrawlerPro 批量爬取任务完成通知 - [状态]
```

### 邮件内容
- **任务概览表格**：总数、成功数、失败数、成功率等
- **失败任务详情**：列出所有失败的UID和错误原因
- **操作建议**：根据结果提供后续处理建议
- **文件信息**：进度文件状态、日志文件位置等

## ⚙️ 配置选项

### 启用/禁用邮件通知

```python
# config/email_config.py
ENABLE_EMAIL_NOTIFICATION = True  # True: 启用, False: 禁用
```

### 支持的邮箱服务商

| 邮箱 | SMTP服务器 | 端口 | 配置支持 |
|------|-----------|------|----------|
| QQ邮箱 | smtp.qq.com | 587 | ✅ 自动配置 |
| 163邮箱 | smtp.163.com | 25 | ✅ 自动配置 |
| 126邮箱 | smtp.126.com | 25 | ✅ 自动配置 |
| Gmail | smtp.gmail.com | 587 | ✅ 自动配置 |
| Outlook | smtp-mail.outlook.com | 587 | ✅ 自动配置 |

程序会根据发送方邮箱自动选择相应的SMTP配置。

## 🚀 使用流程

1. **配置邮件**：按上述步骤配置发送和接收邮箱
2. **测试配置**：运行 `python3 test_email_config.py` 确认配置正确
3. **正常使用**：运行批量爬取命令，任务完成后自动发送邮件

```bash
# 批量爬取命令
python3 main.py --platform bili --type creator --bili_uid_json "/path/to/uid_list.json"
```

4. **查收邮件**：任务完成后检查邮箱，查看详细报告

## 🔍 故障排除

### 常见问题

#### 1. 邮件发送失败
```
❌ 邮件发送失败: Authentication failed
```
**解决方案：**
- 检查邮箱地址是否正确
- 确认使用的是应用密码，不是登录密码
- 确认邮箱服务已开启SMTP服务

#### 2. 网络连接问题
```
❌ 邮件发送失败: Connection timeout
```
**解决方案：**
- 检查网络连接
- 检查防火墙设置
- 尝试更换网络环境

#### 3. 配置文件错误
```
❌ 邮件配置无效，已禁用邮件通知
```
**解决方案：**
- 运行 `python3 test_email_config.py` 检查配置
- 确认邮箱地址格式正确
- 检查配置文件语法

### 调试模式

如需调试，可以查看日志：

```bash
# 查看批量爬取日志
tail -f batch_crawler.log | grep Email
```

### 临时禁用

如果暂时不需要邮件通知：

```python
# config/email_config.py
ENABLE_EMAIL_NOTIFICATION = False
```

## 📝 注意事项

1. **安全性**：
   - 应用密码请妥善保管，不要泄露
   - 建议使用环境变量配置敏感信息
   - 定期更新应用密码

2. **邮箱限制**：
   - 部分邮箱服务商有发送频率限制
   - 建议不要频繁发送测试邮件

3. **网络环境**：
   - 某些网络环境可能阻止SMTP连接
   - 企业网络可能需要配置代理

4. **邮件内容**：
   - 邮件采用HTML格式，支持富文本显示
   - 包含详细的任务统计和失败信息

## 📞 技术支持

如遇到问题，请：

1. 先运行测试命令检查配置
2. 查看错误日志确定问题原因
3. 参考本文档的故障排除部分
4. 确认网络和邮箱服务状态

---

**配置完成后，MediaCrawlerPro 将在每次批量爬取任务完成后自动发送详细的任务报告邮件，让您及时了解爬取结果！** 📧✨ 