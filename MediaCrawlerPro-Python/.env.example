# MediaCrawlerPro Docker 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ===========================================
# 数据库配置
# ===========================================
RELATION_DB_USER=root
RELATION_DB_HOST=mysql_db
RELATION_DB_PWD=123456
RELATION_DB_PORT=3306
RELATION_DB_NAME=media_crawler

# ===========================================
# Redis 配置
# ===========================================
REDIS_DB_HOST=redis_cache
REDIS_DB_PWD=123456
REDIS_DB_PORT=6379
REDIS_DB_NUM=0

# ===========================================
# 签名服务配置
# ===========================================
SIGN_SRV_HOST=mediacrawler_signsrv
SIGN_SRV_PORT=8989

# ===========================================
# HTTP 中间件配置
# ===========================================
HTTP_SERVER_HOST=0.0.0.0
HTTP_SERVER_PORT=8080

# ===========================================
# 代理配置 (可选)
# ===========================================
# 如果需要使用IP代理，请取消注释并填写相应信息
# KDL_SECERT_ID=your_secret_id
# KDL_SIGNATURE=your_signature
# KDL_USER_NAME=your_username
# KDL_USER_PWD=your_password

# ===========================================
# 爬虫配置
# ===========================================
# 默认平台
DEFAULT_PLATFORM=bili

# 默认爬取类型
DEFAULT_TYPE=detail

# BV文件路径
BV_JSON_FILE=/app/bv.json

# 进度文件路径
PROGRESS_FILE=/app/crawler_progress.json

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# ===========================================
# 其他配置
# ===========================================
# 时区设置
TZ=Asia/Shanghai

# 爬取间隔（秒）
CRAWL_INTERVAL=1

# 最大重试次数
MAX_RETRY_COUNT=3
