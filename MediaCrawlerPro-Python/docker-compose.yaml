# Docker Compose配置文件

services:
  app:
    build: .
    container_name: mediacrawlerpro
    ports:
      - "8080:8080"  # HTTP中间件端口 - 公网访问
    restart: unless-stopped
    depends_on:
      - db
      - redis
      - signsrv
    volumes:
      - ./config:/app/config
      # - ./crawler_progress.json:/app/crawler_progress.json  # 临时移除进度文件挂载
    environment:
      - RELATION_DB_USER=root
      - RELATION_DB_HOST=mysql_db
      - RELATION_DB_PWD=123456
      - RELATION_DB_PORT=3306
      - RELATION_DB_NAME=media_crawler
      - REDIS_DB_HOST=redis_cache
      - REDIS_DB_PWD=123456
      - REDIS_DB_PORT=6379
      - REDIS_DB_NUM=0
      - SIGN_SRV_HOST=mediacrawler_signsrv
      - SIGN_SRV_PORT=8989
      # 如果开启IP代理，需要配置以下环境变量，或者在config/proxy_config.py中配置
      # - KDL_SECERT_ID
      # - KDL_SIGNATURE
      # - KDL_USER_NAME
      # - KDL_USER_PWD

    # command: python start_http_server.py  # 启动HTTP中间件服务
    # 使用Dockerfile中的默认CMD，包含数据库等待逻辑

  signsrv:
    build: ../MediaCrawlerPro-SignSrv  # 构建签名服务镜像
    container_name: mediacrawler_signsrv
    # ports:
    #   - "8989:8989"  # 内部服务，不对外开放
    restart: unless-stopped
    environment:
      - APP_PORT=8989
      - APP_HOST=0.0.0.0
      - LOGGER_LEVEL=ERROR
    command: python app.py

  db:
    image: mysql:8.0
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: media_crawler
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3307:3306"  # MySQL数据库 - 可选择性对外开放
    restart: unless-stopped
    volumes:
      - mysql_db_data:/var/lib/mysql
      - ./schema/tables.sql:/docker-entrypoint-initdb.d/01-tables.sql:ro  # 自动初始化表结构
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0

  redis:
    image: redis:7.0
    container_name: redis_cache
    environment:
      REDIS_PASSWORD: 123456
    # ports:
    #   - "6378:6379"  # Redis缓存 - 内部服务，不对外开放
    restart: unless-stopped
    volumes:
      - redis_data:/data
    command: [ "redis-server", "--requirepass", "123456" ]

volumes:
  mysql_db_data:
  redis_data:
