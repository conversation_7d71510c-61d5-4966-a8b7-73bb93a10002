#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动循环运行MediaCrawlerPro爬虫程序
运行指定命令后等待10分钟再重新运行
支持Windows、macOS、Linux跨平台运行
"""

import subprocess
import time
import datetime
import os
import sys
import signal
import logging
import shutil
import threading
import platform

class AutoCrawler:
    def __init__(self):
        # 检测操作系统
        self.is_windows = platform.system().lower() == 'windows'
        
        # Windows下设置环境变量解决编码问题
        if self.is_windows:
            self.setup_windows_encoding()
        
        # 配置日志
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        log_handlers = [
            logging.FileHandler('auto_crawler.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=log_handlers
        )
        self.logger = logging.getLogger(__name__)
        
        # 自动检测Python命令
        self.python_cmd = self.detect_python_command()
        
        # 要运行的命令
        self.command = [self.python_cmd, "main.py", "--platform", "bili", "--type", "creator", "--bili_uid_json", "../1.json"]
        
        # 等待时间（秒）
        self.wait_time = 10 * 60  # 10分钟
        
        # 运行标志
        self.running = True
        self.current_process = None
        
        # 注册信号处理器，用于优雅退出
        self.setup_signal_handlers()
    
    def setup_windows_encoding(self):
        """设置Windows编码环境"""
        try:
            # 设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul 2>&1')
            
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            
            # 尝试设置控制台输出编码
            if hasattr(sys.stdout, 'reconfigure'):
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except:
                    pass
        except Exception as e:
            print(f"设置Windows编码时出现警告: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器，兼容Windows和Unix系统"""
        try:
            signal.signal(signal.SIGINT, self.signal_handler)
            if not self.is_windows:
                # Windows不支持SIGTERM
                signal.signal(signal.SIGTERM, self.signal_handler)
        except Exception as e:
            self.logger.warning(f"设置信号处理器时出现警告: {e}")
    
    def detect_python_command(self):
        """自动检测可用的Python命令"""
        if self.is_windows:
            python_commands = ['python', 'python3', 'py', 'python.exe', 'python3.exe']
        else:
            python_commands = ['python3', 'python', 'python3.9', 'python3.8', 'python3.7']
        
        for cmd in python_commands:
            if shutil.which(cmd):
                try:
                    result = subprocess.run([cmd, '--version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.logger.info(f"检测到Python命令: {cmd} - {result.stdout.strip()}")
                        return cmd
                except Exception:
                    continue
        
        # 如果都找不到，根据系统选择默认命令
        default_cmd = 'python' if self.is_windows else 'python3'
        self.logger.warning(f"无法检测到Python命令，使用默认的{default_cmd}")
        return default_cmd
    
    def signal_handler(self, signum, frame):
        """处理退出信号"""
        self.logger.info(f"接收到退出信号 {signum}，正在停止程序...")
        self.running = False
        if self.current_process:
            self.logger.info("正在终止当前运行的爬虫进程...")
            try:
                if self.is_windows:
                    # Windows下使用terminate()
                    self.current_process.terminate()
                else:
                    # Unix系统下可以使用kill()
                    self.current_process.terminate()
                
                # 等待进程结束，最多等待5秒
                try:
                    self.current_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.logger.warning("进程未能在5秒内正常结束，强制终止...")
                    if self.is_windows:
                        self.current_process.kill()
                    else:
                        self.current_process.kill()
            except Exception as e:
                self.logger.error(f"终止进程时发生错误: {e}")
    
    def print_output_realtime(self, process):
        """实时打印进程输出"""
        try:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 处理编码问题，特别是Windows下的中文显示
                    try:
                        print(output.strip())
                        sys.stdout.flush()
                    except UnicodeEncodeError:
                        # 如果遇到编码问题，尝试用其他方式输出
                        try:
                            print(output.strip().encode('utf-8', errors='ignore').decode('utf-8'))
                            sys.stdout.flush()
                        except:
                            print("[输出包含无法显示的字符]")
                            sys.stdout.flush()
        except Exception as e:
            self.logger.error(f"实时输出时发生错误: {e}")
    
    def run_command(self):
        """运行爬虫命令"""
        try:
            self.logger.info(f"开始运行命令: {' '.join(self.command)}")
            self.logger.info("=" * 60)
            start_time = datetime.datetime.now()
            
            # 设置进程创建参数
            popen_kwargs = {
                'stdout': subprocess.PIPE,
                'stderr': subprocess.STDOUT,
                'text': True,
                'encoding': 'utf-8',
                'bufsize': 1,
                'universal_newlines': True
            }
            
            # 设置环境变量，解决编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            popen_kwargs['env'] = env
            
            # Windows特殊处理
            if self.is_windows:
                popen_kwargs['creationflags'] = subprocess.CREATE_NEW_PROCESS_GROUP
                # Windows下可能需要处理编码问题
                popen_kwargs['encoding'] = 'utf-8'
                popen_kwargs['errors'] = 'ignore'
            
            # 运行命令，实时显示输出
            self.current_process = subprocess.Popen(self.command, **popen_kwargs)
            
            # 创建线程来实时打印输出
            output_thread = threading.Thread(target=self.print_output_realtime, args=(self.current_process,))
            output_thread.daemon = True
            output_thread.start()
            
            # 等待进程完成
            return_code = self.current_process.wait()
            
            # 等待输出线程完成
            output_thread.join(timeout=2)
            
            end_time = datetime.datetime.now()
            duration = end_time - start_time
            
            self.logger.info("=" * 60)
            # 记录运行结果
            if return_code == 0:
                self.logger.info(f"✅ 命令执行成功，耗时: {duration}")
            else:
                self.logger.error(f"❌ 命令执行失败，返回码: {return_code}，耗时: {duration}")
            
            self.current_process = None
            return return_code == 0
            
        except Exception as e:
            self.logger.error(f"运行命令时发生异常: {str(e)}")
            self.current_process = None
            return False
    
    def wait_with_countdown(self):
        """等待指定时间，并显示倒计时"""
        self.logger.info(f"⏰ 等待 {self.wait_time // 60} 分钟后重新运行...")
        
        remaining = self.wait_time
        last_minute_shown = -1
        
        while remaining > 0 and self.running:
            current_minute = remaining // 60
            
            # 每分钟显示一次，或者最后10秒每秒显示
            if (remaining % 60 == 0 and current_minute != last_minute_shown) or remaining <= 10:
                if remaining <= 10:
                    self.logger.info(f"⏳ 还需等待 {remaining} 秒...")
                elif current_minute > 0:
                    self.logger.info(f"⏳ 还需等待 {current_minute} 分钟...")
                last_minute_shown = current_minute
            
            time.sleep(1)
            remaining -= 1
        
        if self.running:
            self.logger.info("✨ 等待结束，准备重新运行...")
    
    def check_environment(self):
        """检查运行环境"""
        self.logger.info("🔍 正在检查运行环境...")
        self.logger.info(f"🖥️  操作系统: {platform.system()} {platform.release()}")
        
        # 检查main.py是否存在
        if not os.path.exists("main.py"):
            self.logger.error("❌ main.py 文件不存在，请确保在正确的目录中运行此脚本")
            return False
        else:
            self.logger.info("✅ main.py 文件存在")
        
        # 检查../1.json是否存在
        json_path = os.path.join("..", "1.json")
        if not os.path.exists(json_path):
            self.logger.warning("⚠️  ../1.json 文件不存在，命令可能会失败")
        else:
            self.logger.info("✅ ../1.json 文件存在")
        
        # 检查Python环境
        try:
            result = subprocess.run([self.python_cmd, "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.logger.info(f"✅ Python版本: {result.stdout.strip()}")
            else:
                self.logger.error(f"❌ Python命令执行失败: {result.stderr}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 无法检查Python版本: {str(e)}")
            return False
        
        return True
    
    def run(self):
        """主运行循环"""
        self.logger.info("🚀 " + "=" * 50)
        self.logger.info("🚀 自动爬虫程序启动")
        self.logger.info(f"📁 当前工作目录: {os.getcwd()}")
        self.logger.info(f"💻 运行命令: {' '.join(self.command)}")
        self.logger.info(f"⏱️  等待间隔: {self.wait_time // 60} 分钟")
        self.logger.info("🚀 " + "=" * 50)
        
        # 检查环境
        if not self.check_environment():
            self.logger.error("❌ 环境检查失败，程序退出")
            return
        
        run_count = 0
        success_count = 0
        fail_count = 0
        
        try:
            while self.running:
                run_count += 1
                self.logger.info(f"\n🔄 第 {run_count} 次运行开始 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.logger.info(f"📊 统计: 成功 {success_count} 次, 失败 {fail_count} 次")
                
                # 运行命令
                success = self.run_command()
                
                if success:
                    success_count += 1
                else:
                    fail_count += 1
                
                if not self.running:
                    break
                
                # 第一次运行后才开始等待
                if run_count == 1:
                    self.logger.info("🎯 第一次运行完成，从下次开始将等待10分钟间隔")
                
                # 等待指定时间（第一次运行后才等待）
                if success:
                    self.logger.info("✅ 命令执行成功，开始等待...")
                    self.wait_with_countdown()
                else:
                    self.logger.warning("⚠️  命令执行失败，仍将等待后重试...")
                    self.wait_with_countdown()
        
        except KeyboardInterrupt:
            self.logger.info("⚠️  接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"❌ 程序运行时发生异常: {str(e)}")
        finally:
            self.logger.info("📊 " + "=" * 50)
            self.logger.info(f"📊 程序结束统计:")
            self.logger.info(f"📊 总运行次数: {run_count}")
            self.logger.info(f"📊 成功次数: {success_count}")
            self.logger.info(f"📊 失败次数: {fail_count}")
            if run_count > 0:
                success_rate = (success_count / run_count) * 100
                self.logger.info(f"📊 成功率: {success_rate:.1f}%")
            self.logger.info("📊 " + "=" * 50)

def main():
    """主函数"""
    # 检测操作系统并显示相应提示
    system = platform.system()
    if system == "Windows":
        print("🖥️  Windows系统检测")
        print("💡 提示: 在Windows下按 Ctrl+C 停止程序")
        print("🔧 正在设置UTF-8编码环境...")
    elif system == "Darwin":
        print("🍎 macOS系统检测")
        print("💡 提示: 按 Ctrl+C 或 Command+C 停止程序")
    else:
        print("🐧 Linux系统检测")
        print("💡 提示: 按 Ctrl+C 停止程序")
    
    print("🤖 自动爬虫程序")
    print("🎯 第一次运行立即开始，之后每次间隔10分钟")
    print("📝 运行日志将保存到 auto_crawler.log 文件")
    print("-" * 50)
    
    crawler = AutoCrawler()
    crawler.run()

if __name__ == "__main__":
    main() 