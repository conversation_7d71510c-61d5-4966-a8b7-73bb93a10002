#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP服务器启动脚本
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖"""
    try:
        import flask
        print("✅ Flask 已安装")
    except ImportError:
        print("❌ Flask 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
        print("✅ Flask 安装完成")

def main():
    """主函数"""
    print("🚀 启动 MediaCrawlerPro HTTP 中间件...")
    
    # 检查依赖
    check_dependencies()
    
    # 切换到脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # 启动HTTP服务器
    try:
        from http_middleware import run_server
        run_server(host='0.0.0.0', port=8080, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == '__main__':
    main()
