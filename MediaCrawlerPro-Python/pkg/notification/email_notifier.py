# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。


# -*- coding: utf-8 -*-
import os
import time
import smtplib
import ssl
from datetime import datetime
from typing import Dict, Optional, List
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from pkg.tools import utils
from config import email_config

EMAIL_AVAILABLE = True


class EmailNotifier:
    """邮件通知服务"""
    
    def __init__(self):
        self.sender_email = email_config.SENDER_EMAIL
        self.sender_password = email_config.SENDER_PASSWORD
        self.receiver_emails = email_config.RECEIVER_EMAILS
        self.enable_notification = email_config.ENABLE_EMAIL_NOTIFICATION and EMAIL_AVAILABLE
        
        if not self.enable_notification:
            utils.logger.info("[EmailNotifier] 邮件通知已禁用")
            return
            
        # 验证配置
        if not self._validate_config():
            self.enable_notification = False
            utils.logger.warning("[EmailNotifier] 邮件配置无效，已禁用邮件通知")
    
    def _validate_config(self) -> bool:
        """验证邮件配置"""
        if not self.sender_email or self.sender_email == "<EMAIL>":
            utils.logger.warning("[EmailNotifier] 发送方邮箱未配置")
            return False
            
        if not self.sender_password or self.sender_password == "your_app_password":
            utils.logger.warning("[EmailNotifier] 邮箱密码未配置")
            return False
            
        if not self.receiver_emails or self.receiver_emails == ["<EMAIL>"]:
            utils.logger.warning("[EmailNotifier] 接收方邮箱未配置")
            return False
            
        return True
    
    def send_task_completion_notification(self, task_data: Dict) -> bool:
        """
        发送任务完成通知
        Args:
            task_data: 任务数据，包含统计信息、失败列表等
        Returns:
            bool: 发送是否成功
        """
        if not self.enable_notification:
            return False
            
        try:
            # 准备邮件内容
            subject = self._generate_subject(task_data)
            body = self._generate_body(task_data)
            
            # 获取SMTP配置
            smtp_config = email_config.get_smtp_config(self.sender_email)
            
            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.sender_email
            msg['To'] = ', '.join(self.receiver_emails)
            
            # 添加HTML内容
            html_part = MIMEText(body, 'html', 'utf-8')
            msg.attach(html_part)
            
            # 尝试发送邮件 - 先尝试主配置，失败后尝试备用配置
            configs_to_try = [smtp_config]
            if 'backup_configs' in smtp_config:
                configs_to_try.extend(smtp_config['backup_configs'])
            
            last_error = None
            for i, config in enumerate(configs_to_try):
                try:
                    success = self._try_send_email(msg, config)
                    if success:
                        if i > 0:
                            utils.logger.info(f"[EmailNotifier] 使用备用配置#{i}发送成功")
                        else:
                            utils.logger.info(f"[EmailNotifier] 邮件发送成功")
                        return True
                except Exception as e:
                    last_error = e
                    utils.logger.warning(f"[EmailNotifier] 配置#{i+1}发送失败: {str(e)}")
                    continue
            
            # 所有配置都失败
            utils.logger.error(f"[EmailNotifier] 所有SMTP配置都失败，最后错误: {str(last_error)}")
            return False
            
        except Exception as e:
            utils.logger.error(f"[EmailNotifier] 邮件发送失败: {str(e)}")
            return False
    
    def _try_send_email(self, msg: MIMEMultipart, smtp_config: Dict) -> bool:
        """
        尝试使用指定配置发送邮件
        Args:
            msg: 邮件对象
            smtp_config: SMTP配置
        Returns:
            bool: 发送是否成功
        """
        # 发送邮件
        if smtp_config.get("ssl", False):
            # 使用SSL连接（QQ邮箱465端口）
            context = ssl.create_default_context()
            server = None
            try:
                timeout = smtp_config.get("timeout", 30)
                server = smtplib.SMTP_SSL(smtp_config["host"], smtp_config["port"], 
                                        context=context, timeout=timeout)
                server.login(self.sender_email, self.sender_password)
                result = server.sendmail(self.sender_email, self.receiver_emails, msg.as_string())
                # 检查发送结果，空字典表示成功
                if not result:
                    utils.logger.info(f"[EmailNotifier] SSL方式发送成功 ({smtp_config['host']}:{smtp_config['port']})")
                    return True
                else:
                    utils.logger.warning(f"[EmailNotifier] 部分邮件发送失败: {result}")
                    return False
            finally:
                if server:
                    try:
                        server.quit()
                    except:
                        # 忽略退出时的错误（QQ邮箱常见）
                        pass
        else:
            # 使用STARTTLS连接（其他邮箱）
            server = None
            try:
                timeout = smtp_config.get("timeout", 30)
                server = smtplib.SMTP(smtp_config["host"], smtp_config["port"], timeout=timeout)
                if smtp_config.get("starttls", False):
                    server.starttls()
                server.login(self.sender_email, self.sender_password)
                result = server.sendmail(self.sender_email, self.receiver_emails, msg.as_string())
                # 检查发送结果，空字典表示成功
                if not result:
                    utils.logger.info(f"[EmailNotifier] STARTTLS方式发送成功 ({smtp_config['host']}:{smtp_config['port']})")
                    return True
                else:
                    utils.logger.warning(f"[EmailNotifier] 部分邮件发送失败: {result}")
                    return False
            finally:
                if server:
                    try:
                        server.quit()
                    except:
                        # 忽略退出时的错误
                        pass
        
        return False
    
    def _generate_subject(self, task_data: Dict) -> str:
        """生成邮件主题"""
        failed_count = task_data.get("failed_count", 0)
        success_count = task_data.get("success_count", 0)
        
        if failed_count == 0:
            status = "全部成功"
        elif success_count == 0:
            status = "全部失败"
        else:
            status = f"部分成功({success_count}成功/{failed_count}失败)"
            
        return email_config.EMAIL_SUBJECT_TEMPLATE.format(status=status)
    
    def _generate_body(self, task_data: Dict) -> str:
        """生成邮件正文"""
        # 基本统计信息
        total_count = task_data.get("total_count", 0)
        success_count = task_data.get("success_count", 0)
        failed_count = task_data.get("failed_count", 0)
        skipped_count = task_data.get("skipped_count", 0)
        
        # 计算成功率
        success_rate = round((success_count / total_count * 100), 2) if total_count > 0 else 0
        
        # 时间信息
        start_time = task_data.get("start_time", "未知")
        end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        duration = self._calculate_duration(task_data.get("start_time_ts", time.time()))
        
        # 任务状态
        if failed_count == 0:
            overall_status = "✅ 全部成功"
            status_class = "status-success"
        elif success_count == 0:
            overall_status = "❌ 全部失败"
            status_class = "status-failed"
        else:
            overall_status = "⚠️ 部分成功"
            status_class = "status-partial"
        
        # 失败任务详情
        failed_section = ""
        if failed_count > 0:
            failed_details = self._format_failed_tasks(task_data.get("failed_uids", []), 
                                                     task_data.get("retry_count", {}))
            failed_section = email_config.FAILED_TASKS_TEMPLATE.format(failed_details=failed_details)
        
        # 建议
        suggestions = self._generate_suggestions(task_data)
        
        # 进度文件状态
        progress_file_status = task_data.get("progress_file_status", "未知")
        
        # 日志文件
        log_file = task_data.get("log_file", "batch_crawler.log")
        
        # 当前时间戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 格式化邮件模板
        return email_config.EMAIL_BODY_TEMPLATE.format(
            status_class=status_class,
            overall_status=overall_status,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            total_count=total_count,
            success_count=success_count,
            failed_count=failed_count,
            skipped_count=skipped_count,
            success_rate=success_rate,
            failed_section=failed_section,
            suggestions=suggestions,
            progress_file_status=progress_file_status,
            log_file=log_file,
            timestamp=timestamp
        )
    
    def _format_failed_tasks(self, failed_uids: List[str], retry_count: Dict) -> str:
        """格式化失败任务详情"""
        if not failed_uids:
            return ""
            
        details = []
        for uid in failed_uids:
            error_info = retry_count.get(uid, "未知错误")
            details.append(f"<li><strong>{uid}:</strong> {error_info}</li>")
        
        return "\n".join(details)
    
    def _generate_suggestions(self, task_data: Dict) -> str:
        """生成操作建议"""
        failed_count = task_data.get("failed_count", 0)
        success_count = task_data.get("success_count", 0)
        
        if failed_count == 0:
            return """
            <ul>
                <li>🎉 恭喜！所有任务都已成功完成</li>
                <li>📊 数据已保存到数据库，可以开始分析</li>
                <li>🔄 进度文件已自动清理，下次可以开始新的批量任务</li>
            </ul>
            """
        else:
            return f"""
            <ul>
                <li>⚠️ 有 {failed_count} 个任务失败，建议检查错误原因</li>
                <li>🔄 可以重新运行相同命令，程序会自动跳过成功的任务</li>
                <li>🛠️ 或使用 <code>python3 progress_manager.py --status</code> 查看详细状态</li>
                <li>🔧 使用 <code>python3 progress_manager.py --reset-failed</code> 重置失败项</li>
            </ul>
            """
    
    def _calculate_duration(self, start_timestamp: float) -> str:
        """计算任务耗时"""
        duration_seconds = int(time.time() - start_timestamp)
        hours = duration_seconds // 3600
        minutes = (duration_seconds % 3600) // 60
        seconds = duration_seconds % 60
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"
    
    def test_email_config(self) -> bool:
        """测试邮件配置"""
        if not self.enable_notification:
            print("❌ 邮件通知未启用或配置无效")
            return False
        
        try:
            # 创建测试数据
            test_data = {
                "total_count": 100,
                "success_count": 95,
                "failed_count": 5,
                "skipped_count": 0,
                "start_time": "2024-06-24 10:00:00",
                "start_time_ts": time.time() - 3600,  # 1小时前
                "failed_uids": ["test_uid_1", "test_uid_2"],
                "retry_count": {
                    "test_uid_1": "连接超时",
                    "test_uid_2": "代理验证失败"
                },
                "progress_file_status": "已保留（存在失败项）",
                "log_file": "batch_crawler.log"
            }
            
            print("📧 正在发送测试邮件...")
            print("🔍 尝试不同的SMTP配置...")
            
            success = self.send_task_completion_notification(test_data)
            
            if success:
                print("✅ 测试邮件发送成功！")
                print(f"📬 邮件已发送到: {', '.join(self.receiver_emails)}")
                return True
            else:
                print("❌ 测试邮件发送失败")
                print("💡 请检查以下项目:")
                print("   1. 网络连接是否正常")
                print("   2. 防火墙是否阻止SMTP连接")
                print("   3. 邮箱密码是否正确（应用密码）")
                print("   4. 邮箱SMTP服务是否已开启")
                return False
                
        except Exception as e:
            print(f"❌ 邮件配置测试失败: {str(e)}")
            return False


def test_email_notification():
    """测试邮件通知功能"""
    notifier = EmailNotifier()
    return notifier.test_email_config()


if __name__ == "__main__":
    test_email_notification() 