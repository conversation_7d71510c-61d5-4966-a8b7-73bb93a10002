FROM python:3.9-slim

WORKDIR /app

# 使用国内源加速
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

COPY requirements.txt .

RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    cmake \
    pkg-config \
    libffi-dev \
    && pip3 install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt \
    && pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple flask pymysql \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*  # 清理APT缓存目录

# 复制配置文件
COPY config/ ./config/

# 复制其他文件
COPY . .

# 确保配置文件存在并有正确权限
RUN ls -la config/ && \
    test -f config/__init__.py && \
    test -f config/base_config.py && \
    echo "配置文件复制成功"

# 给启动脚本添加执行权限
RUN chmod +x start-with-db-wait.sh wait-for-mysql.py

# 暴露HTTP中间件端口
EXPOSE 8080

# 使用启动脚本，等待数据库后启动服务
CMD ["./start-with-db-wait.sh"]