# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。


import asyncio
import sys
from typing import Dict, Optional, Type
import json
import os
import logging
import time
from datetime import datetime

from pkg.notification.email_notifier import EmailNotifier

import cmd_arg
import config
import constant
import db
from base.base_crawler import AbstractCrawler
from media_platform.bilibili import BilibiliCrawler
from media_platform.douyin import DouYinCrawler
from media_platform.kuaishou import KuaiShouCrawler
from media_platform.tieba import TieBaCrawler
from media_platform.weibo import WeiboCrawler
from media_platform.xhs import XiaoHongShuCrawler
from media_platform.zhihu import ZhihuCrawler
from constant import MYSQL_ACCOUNT_SAVE


class CrawlerFactory:
    CRAWLERS: Dict[str, AbstractCrawler] = {
        constant.XHS_PLATFORM_NAME: XiaoHongShuCrawler,
        constant.WEIBO_PLATFORM_NAME: WeiboCrawler,
        constant.TIEBA_PLATFORM_NAME: TieBaCrawler,
        constant.BILIBILI_PLATFORM_NAME: BilibiliCrawler,
        constant.DOUYIN_PLATFORM_NAME: DouYinCrawler,
        constant.KUAISHOU_PLATFORM_NAME: KuaiShouCrawler,
        constant.ZHIHU_PLATFORM_NAME: ZhihuCrawler,
    }

    @staticmethod
    def create_crawler(platform: str) -> AbstractCrawler:
        """
        Create a crawler instance by platform
        Args:
            platform:

        Returns:

        """
        crawler_class: Optional[Type[AbstractCrawler]] = CrawlerFactory.CRAWLERS.get(
            platform
        )
        if not crawler_class:
            raise ValueError(
                "Invalid Media Platform Currently only supported xhs or dy or ks or bili ..."
            )
        return crawler_class()


class BatchCrawlerManager:
    """批量爬取管理器，支持断点继续和详细日志"""
    
    def __init__(self, progress_file: str = None, log_file: str = None):
        self.progress_file = progress_file or config.PROGRESS_FILE_PATH
        self.log_file = log_file or config.BATCH_CRAWLER_LOG_FILE
        self.logger = self._setup_logger()
        self.progress_data = self._load_progress()
        self.email_notifier = EmailNotifier()  # 初始化邮件通知器
        self.start_time_ts = None  # 记录任务开始时间戳
    
    def _setup_logger(self):
        """设置批量爬取日志"""
        logger = logging.getLogger('batch_crawler')
        logger.setLevel(logging.INFO)
        
        # 清除已有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 文件处理器
        if config.ENABLE_BATCH_LOG:
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def _load_progress(self) -> dict:
        """加载进度数据"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.logger.info(f"已加载进度文件: {self.progress_file}")
                # 确保数据结构完整性
                return self._ensure_progress_structure(data)
            except Exception as e:
                self.logger.error(f"加载进度文件失败: {e}")
                return self._create_empty_progress()
        else:
            self.logger.info("未找到进度文件，创建新的进度记录")
            return self._create_empty_progress()

    def _ensure_progress_structure(self, data: dict) -> dict:
        """确保进度数据结构完整性，补充缺失的字段"""
        template = self._create_empty_progress()

        # 合并数据，确保所有必需字段都存在
        for key, default_value in template.items():
            if key not in data:
                data[key] = default_value
                self.logger.info(f"补充缺失的进度字段: {key}")

        return data
    
    def _create_empty_progress(self) -> dict:
        """创建空的进度数据结构"""
        return {
            "start_time": datetime.now().isoformat(),
            "last_update": datetime.now().isoformat(),
            "total_uids": 0,
            "processed_uids": [],
            "failed_uids": [],
            "skipped_uids": [],
            "current_processing": None,
            "retry_count": {},
            "statistics": {
                "success_count": 0,
                "failed_count": 0,
                "skipped_count": 0
            },
            "pending_uids": [],
            "original_total": 0
        }
    
    def _save_progress(self):
        """保存进度数据"""
        try:
            self.progress_data["last_update"] = datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存进度文件失败: {e}")
    
    def initialize_batch(self, uid_list: list):
        """初始化批量处理任务"""
        if not self.progress_data.get("total_uids"):
            self.progress_data["total_uids"] = len(uid_list)
            self.progress_data["start_time"] = datetime.now().isoformat()
            self.start_time_ts = time.time()  # 记录开始时间戳用于邮件通知
            self._save_progress()
        
        processed = set(self.progress_data.get("processed_uids", []))
        failed = set(self.progress_data.get("failed_uids", []))
        skipped = set(self.progress_data.get("skipped_uids", []))
        
        # 修改逻辑：包含失败的任务以便重新处理
        # 只排除已成功处理和已跳过的，失败的任务应该重新处理
        remaining_uids = [uid for uid in uid_list if uid not in processed and uid not in skipped]
        
        # 分别统计待处理和重试的数量
        retry_uids = [uid for uid in uid_list if uid in failed]
        new_uids = [uid for uid in uid_list if uid not in processed and uid not in failed and uid not in skipped]
        
        # 记录待处理列表和原始总数（用于完成度判断）
        self.progress_data["pending_uids"] = remaining_uids
        self.progress_data["original_total"] = len(uid_list)
        self._save_progress()
        
        self.logger.info(f"=== 批量爬取任务初始化 ===")
        self.logger.info(f"总ID数量: {len(uid_list)}")
        self.logger.info(f"已处理: {len(processed)}")
        self.logger.info(f"处理失败: {len(failed)}")
        self.logger.info(f"已跳过: {len(skipped)}")
        self.logger.info(f"待处理: {len(remaining_uids)}")
        
        if retry_uids:
            self.logger.info(f"重试失败: {len(retry_uids)}")
        if new_uids:
            self.logger.info(f"全新处理: {len(new_uids)}")
        
        if remaining_uids:
            if retry_uids:
                self.logger.info(f"将重新处理 {len(retry_uids)} 个失败的任务")
            self.logger.info(f"将从ID {remaining_uids[0]} 开始继续处理")
        else:
            self.logger.info("所有ID均已处理完成")
        
        return remaining_uids
    
    def mark_processing(self, uid: str):
        """标记当前正在处理的UID"""
        self.progress_data["current_processing"] = uid
        self._save_progress()
        self.logger.info(f"开始处理UID: {uid}")
    
    def mark_success(self, uid: str):
        """标记UID处理成功"""
        if uid not in self.progress_data["processed_uids"]:
            self.progress_data["processed_uids"].append(uid)
        self.progress_data["current_processing"] = None
        self.progress_data["statistics"]["success_count"] += 1
        
        # 从失败列表中移除（如果存在）
        if uid in self.progress_data["failed_uids"]:
            self.progress_data["failed_uids"].remove(uid)
        
        # 重置重试计数
        if uid in self.progress_data["retry_count"]:
            del self.progress_data["retry_count"][uid]
        
        self._save_progress()
        self.logger.info(f"✅ UID {uid} 处理成功")
    
    def mark_failed(self, uid: str, error: str = ""):
        """标记UID处理失败"""
        # 直接标记为失败，不进行重试
        if uid not in self.progress_data["failed_uids"]:
            self.progress_data["failed_uids"].append(uid)
            self.progress_data["statistics"]["failed_count"] += 1
        self.progress_data["current_processing"] = None
        
        # 记录错误信息到重试计数中用于追踪
        self.progress_data["retry_count"][uid] = error
        
        self.logger.error(f"❌ UID {uid} 处理失败: {error}")
        self._save_progress()
    
    def should_retry(self, uid: str) -> bool:
        """检查是否应该重试（已禁用重试机制）"""
        return False  # 不再重试，直接返回False
    
    def mark_skipped(self, uid: str, reason: str = ""):
        """标记UID被跳过"""
        if uid not in self.progress_data["skipped_uids"]:
            self.progress_data["skipped_uids"].append(uid)
        self.progress_data["current_processing"] = None
        self.progress_data["statistics"]["skipped_count"] += 1
        self._save_progress()
        self.logger.info(f"⏭️ UID {uid} 已跳过: {reason}")
    
    def print_final_summary(self):
        """打印最终统计结果并发送邮件通知"""
        stats = self.progress_data["statistics"]
        total = stats["success_count"] + stats["failed_count"] + stats["skipped_count"]
        
        self.logger.info(f"\n=== 批量爬取任务完成 ===")
        self.logger.info(f"总处理数量: {total}")
        self.logger.info(f"成功: {stats['success_count']}")
        self.logger.info(f"失败: {stats['failed_count']}")
        self.logger.info(f"跳过: {stats['skipped_count']}")
        
        if self.progress_data["failed_uids"]:
            self.logger.info(f"失败的ID列表: {self.progress_data['failed_uids']}")
            self._show_failed_task_guidance()
        
        # 检查是否所有任务都已完成且全部成功
        all_completed_successfully = self._check_all_tasks_completed_successfully()
        
        # 准备邮件通知数据
        progress_file_status = ""
        if all_completed_successfully:
            self.logger.info(f"🎉 所有任务已完成且全部成功，开始备份和重置进度文件...")
            self._backup_and_reset_progress()
            progress_file_status = "已备份并删除（全部成功）"
        else:
            self.logger.info(f"⚠️ 存在失败任务或仍有未完成的任务，保留进度文件以便重新处理失败项")
            if self.progress_data["failed_uids"]:
                self._show_retry_guidance()
            progress_file_status = "已保留（存在失败项）"
        
        # 发送邮件通知
        self._send_completion_email(progress_file_status)
    
    def _check_all_tasks_completed(self) -> bool:
        """检查是否所有任务都已完成（不管成功失败）"""
        processed_count = len(self.progress_data["processed_uids"])
        failed_count = len(self.progress_data["failed_uids"])
        skipped_count = len(self.progress_data.get("skipped_uids", []))
        
        # 使用原始总数判断是否完成
        original_total = self.progress_data.get("original_total", 0)
        completed_tasks = processed_count + failed_count + skipped_count
        
        self.logger.info(f"任务完成情况: {completed_tasks}/{original_total}")
        self.logger.info(f"  - 成功: {processed_count}")
        self.logger.info(f"  - 失败: {failed_count}")
        self.logger.info(f"  - 跳过: {skipped_count}")
        
        # 如果处理总数等于原始总数，则认为全部完成
        is_completed = completed_tasks == original_total and original_total > 0
        
        return is_completed
    
    def _check_all_tasks_completed_successfully(self) -> bool:
        """检查是否所有任务都已完成且全部成功（无失败项）"""
        processed_count = len(self.progress_data["processed_uids"])
        failed_count = len(self.progress_data["failed_uids"])
        skipped_count = len(self.progress_data.get("skipped_uids", []))

        # 使用原始总数判断是否完成
        original_total = self.progress_data.get("original_total", 0)

        # 如果没有original_total，尝试使用total_uids作为备选
        if original_total == 0:
            original_total = self.progress_data.get("total_uids", 0)
            if original_total > 0:
                self.logger.info(f"使用total_uids作为原始总数: {original_total}")
                # 更新original_total字段以便下次使用
                self.progress_data["original_total"] = original_total
                self._save_progress()

        completed_tasks = processed_count + failed_count + skipped_count

        # 只有当全部完成且没有失败项时才返回True
        is_all_completed = completed_tasks == original_total and original_total > 0
        has_no_failures = failed_count == 0

        self.logger.info(f"完成检查: 已处理={processed_count}, 失败={failed_count}, 跳过={skipped_count}, 总计={completed_tasks}, 原始总数={original_total}")

        if is_all_completed and has_no_failures:
            self.logger.info("🎉 所有任务处理完毕且全部成功，符合自动重置条件")
            return True
        elif is_all_completed and not has_no_failures:
            self.logger.info(f"⚠️ 所有任务已处理完毕，但有 {failed_count} 个失败项，保留进度文件")
            return False
        else:
            self.logger.info("⏳ 仍有任务未完成，保留进度文件")
            return False
    
    def _backup_and_reset_progress(self):
        """备份进度文件并重置状态（仅在全部成功时执行）"""
        try:
            # 1. 创建备份文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{self.progress_file}.backup_success_{timestamp}"
            
            import shutil
            import os
            
            if os.path.exists(self.progress_file):
                shutil.copy2(self.progress_file, backup_file)
                self.logger.info(f"📄 进度文件已备份至: {backup_file}")
                
                # 2. 删除原进度文件以便下次重新开始
                os.remove(self.progress_file)
                self.logger.info(f"🗑️ 已删除进度文件: {self.progress_file}")
                self.logger.info(f"✨ 所有任务成功完成，下次执行将从头开始")
            else:
                self.logger.warning(f"⚠️ 进度文件不存在: {self.progress_file}")
                
        except Exception as e:
            self.logger.error(f"❌ 备份和重置进度文件失败: {e}")
            self.logger.error(f"💡 请手动备份和删除文件: {self.progress_file}")
    
    def _show_failed_task_guidance(self):
        """显示失败任务的处理指导"""
        failed_count = len(self.progress_data["failed_uids"])
        self.logger.info(f"\n=== 失败任务处理指导 ===")
        self.logger.info(f"📊 失败任务数量: {failed_count}")
        self.logger.info(f"📋 失败任务详情:")
        for uid in self.progress_data["failed_uids"]:
            error_info = self.progress_data["retry_count"].get(uid, "未知错误")
            self.logger.info(f"  - {uid}: {error_info}")
    
    def _show_retry_guidance(self):
        """显示重新处理失败任务的指导"""
        self.logger.info(f"\n=== 重新处理指导 ===")
        self.logger.info(f"💡 进度文件已保留，您可以:")
        self.logger.info(f"   1. 直接重新运行相同命令，程序会自动跳过成功的任务，只处理失败的任务")
        self.logger.info(f"   2. 使用进度管理工具查看状态: python3 progress_manager.py --status")
        self.logger.info(f"   3. 重置失败项状态: python3 progress_manager.py --reset-failed")
        self.logger.info(f"   4. 手动清理进度文件: python3 progress_manager.py --clean")
        self.logger.info(f"📂 进度文件位置: {self.progress_file}")
    
    def _send_completion_email(self, progress_file_status: str):
        """发送任务完成邮件通知"""
        try:
            stats = self.progress_data["statistics"]
            
            # 准备邮件数据
            task_data = {
                "total_count": self.progress_data.get("original_total", 0),
                "success_count": stats["success_count"],
                "failed_count": stats["failed_count"],
                "skipped_count": stats["skipped_count"],
                "start_time": self.progress_data.get("start_time", "未知"),
                "start_time_ts": self.start_time_ts or time.time(),
                "failed_uids": self.progress_data.get("failed_uids", []),
                "retry_count": self.progress_data.get("retry_count", {}),
                "progress_file_status": progress_file_status,
                "log_file": os.path.basename(self.log_file)
            }
            
            # 发送邮件
            self.logger.info("📧 正在发送邮件通知...")
            success = self.email_notifier.send_task_completion_notification(task_data)
            
            if success:
                self.logger.info("✅ 邮件通知发送成功")
            else:
                self.logger.warning("⚠️ 邮件通知发送失败或已禁用")
                
        except Exception as e:
            self.logger.error(f"❌ 发送邮件通知时出错: {str(e)}")


async def process_single_uid(uid: str, crawler_manager: BatchCrawlerManager) -> bool:
    """处理单个UID，返回是否成功"""
    try:
        crawler_manager.mark_processing(uid)
        
        # 设置当前要爬取的UID
        config.BILI_CREATOR_ID_LIST = [uid]
        
        # 创建并初始化爬虫
        crawler = CrawlerFactory.create_crawler(platform=config.PLATFORM)
        await crawler.async_initialize()
        await crawler.start()
        
        crawler_manager.mark_success(uid)
        return True
        
    except Exception as e:
        error_msg = str(e)
        crawler_manager.mark_failed(uid, error_msg)
        return False


def extract_bv_from_url(url: str) -> str:
    """从B站链接中提取BV号"""
    import re
    
    # 清理空白字符
    url = url.strip()
    
    # 正则表达式匹配BV号
    # 支持格式：https://www.bilibili.com/video/BV1xxxxx/
    # BV号由BV + 10位字母数字组成
    bv_pattern = r'BV[a-zA-Z0-9]{10}'
    match = re.search(bv_pattern, url)
    
    if match:
        return match.group()
    else:
        raise ValueError(f"无法从链接中提取BV号: {url}")


def parse_bv_txt_file(file_path: str) -> list:
    """解析txt文件，提取所有BV号"""
    bv_list = []
    failed_lines = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:  # 跳过空行
                continue

            try:
                bv_id = extract_bv_from_url(line)
                if bv_id not in bv_list:  # 去重
                    bv_list.append(bv_id)
                else:
                    print(f"警告：第{i}行的BV号重复，已跳过: {bv_id}")
            except ValueError as e:
                failed_lines.append(f"第{i}行: {line} - {str(e)}")

        if failed_lines:
            print(f"警告：以下{len(failed_lines)}行无法解析:")
            for failed in failed_lines:
                print(f"  {failed}")

        print(f"成功从txt文件中提取到 {len(bv_list)} 个唯一的BV号")
        return bv_list

    except Exception as e:
        raise Exception(f"读取txt文件失败: {e}")


def parse_complex_bv_json_file(file_path: str) -> list:
    """解析复杂格式的BV号JSON文件，支持嵌套结构"""
    bv_list = []
    failed_items = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"开始解析复杂JSON文件: {file_path}")

        # 处理数组格式的JSON
        if isinstance(data, list):
            print(f"检测到数组格式，共 {len(data)} 个顶级元素")
            for i, item in enumerate(data):
                try:
                    # 检查是否有 data.items 结构
                    if isinstance(item, dict) and "data" in item and "items" in item["data"]:
                        items = item["data"]["items"]
                        print(f"第{i+1}个顶级元素包含 {len(items)} 个items")

                        for j, sub_item in enumerate(items):
                            try:
                                extracted_bvs = _extract_bv_from_complex_item(sub_item, f"{i+1}-{j+1}")
                                for bv_id in extracted_bvs:
                                    if bv_id not in bv_list:  # 去重
                                        bv_list.append(bv_id)
                                        print(f"  提取BV号: {bv_id}")
                                    else:
                                        print(f"  警告：BV号重复，已跳过: {bv_id}")
                            except Exception as e:
                                failed_items.append(f"第{i+1}-{j+1}项: {str(e)}")
                    else:
                        # 直接处理顶级元素
                        extracted_bvs = _extract_bv_from_complex_item(item, i+1)
                        for bv_id in extracted_bvs:
                            if bv_id not in bv_list:  # 去重
                                bv_list.append(bv_id)
                                print(f"  提取BV号: {bv_id}")
                            else:
                                print(f"  警告：BV号重复，已跳过: {bv_id}")
                except Exception as e:
                    failed_items.append(f"第{i+1}项: {str(e)}")

        # 处理单个对象格式的JSON
        elif isinstance(data, dict):
            try:
                extracted_bvs = _extract_bv_from_complex_item(data, 0)
                for bv_id in extracted_bvs:
                    if bv_id not in bv_list:  # 去重
                        bv_list.append(bv_id)
            except Exception as e:
                failed_items.append(f"根对象: {str(e)}")

        else:
            raise Exception("不支持的JSON格式，期望数组或对象")

        if failed_items:
            print(f"警告：以下{len(failed_items)}项无法解析:")
            for failed in failed_items:
                print(f"  {failed}")

        print(f"成功从复杂JSON文件中提取到 {len(bv_list)} 个唯一的BV号")
        return bv_list

    except Exception as e:
        raise Exception(f"读取复杂JSON文件失败: {e}")


def _extract_bv_from_complex_item(item: dict, index: int) -> list:
    """从复杂的JSON项中提取BV号"""
    bv_list = []

    def _recursive_search(obj, path=""):
        """递归搜索BV号"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if key == "bv" and isinstance(value, list):
                    # 处理 "bv": [{"text": "BV1xxx", "type": "text"}] 格式
                    for bv_item in value:
                        if isinstance(bv_item, dict) and "text" in bv_item:
                            bv_text = bv_item["text"].strip()
                            if bv_text.startswith("BV") and len(bv_text) == 12:
                                bv_list.append(bv_text)
                elif key == "text" and isinstance(value, str) and value.startswith("BV"):
                    # 处理直接的BV号文本
                    bv_text = value.strip()
                    if len(bv_text) == 12:
                        bv_list.append(bv_text)
                else:
                    _recursive_search(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                _recursive_search(item, f"{path}[{i}]")

    _recursive_search(item)

    if not bv_list:
        raise ValueError(f"未找到有效的BV号")

    return bv_list


def parse_bv_json_file_smart(file_path: str) -> list:
    """智能解析BV号JSON文件，自动识别简单和复杂格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 检查是否为简单的字符串数组格式
        if isinstance(data, list) and len(data) > 0:
            # 检查第一个元素
            first_item = data[0]
            if isinstance(first_item, str):
                # 简单格式：["BV1xxx", "BV2xxx", ...]
                print("检测到简单JSON格式（字符串数组）")
                bv_list = []
                for item in data:
                    if isinstance(item, str) and item.strip().startswith("BV"):
                        bv_text = item.strip()
                        if len(bv_text) == 12 and bv_text not in bv_list:
                            bv_list.append(bv_text)
                print(f"从简单JSON格式中提取到 {len(bv_list)} 个BV号")
                return bv_list
            else:
                # 复杂格式，使用复杂解析器
                print("检测到复杂JSON格式（嵌套结构）")
                return parse_complex_bv_json_file(file_path)

        elif isinstance(data, dict):
            # 单个对象格式，使用复杂解析器
            print("检测到复杂JSON格式（单个对象）")
            return parse_complex_bv_json_file(file_path)

        else:
            raise Exception("不支持的JSON格式")

    except Exception as e:
        raise Exception(f"智能解析JSON文件失败: {e}")


async def process_single_bv(bv_id: str, crawler_manager: BatchCrawlerManager) -> bool:
    """处理单个BV号，返回是否成功"""
    try:
        crawler_manager.mark_processing(bv_id)
        
        # 设置当前要爬取的BV号
        config.BILI_SPECIFIED_ID_LIST = [bv_id]
        
        # 创建并初始化爬虫
        crawler = CrawlerFactory.create_crawler(platform=config.PLATFORM)
        await crawler.async_initialize()
        await crawler.start()
        
        crawler_manager.mark_success(bv_id)
        return True
        
    except Exception as e:
        error_msg = str(e)
        crawler_manager.mark_failed(bv_id, error_msg)
        return False


async def cooldown_with_display(duration: int, logger, display_interval: int = 60):
    """
    冷却等待，带倒计时显示
    Args:
        duration: 冷却时间（秒）
        logger: 日志记录器
        display_interval: 显示间隔（秒）
    """
    import time
    
    start_time = time.time()
    end_time = start_time + duration
    
    logger.info(f"🧊 开始冷却等待 {duration} 秒 ({duration//60} 分钟)")
    
    while time.time() < end_time:
        remaining = int(end_time - time.time())
        if remaining <= 0:
            break
            
        # 显示剩余时间
        minutes = remaining // 60
        seconds = remaining % 60
        logger.info(f"⏰ 冷却中... 剩余时间: {minutes}分{seconds}秒")
        
        # 等待显示间隔时间或剩余时间（取较小值）
        sleep_time = min(display_interval, remaining)
        await asyncio.sleep(sleep_time)
    
    logger.info("✅ 冷却完成，继续处理...")


async def process_batch_uids(uid_list: list, crawler_manager: BatchCrawlerManager):
    """批量处理UID列表"""
    import random
    
    remaining_uids = crawler_manager.initialize_batch(uid_list)
    
    if not remaining_uids:
        crawler_manager.logger.info("没有需要处理的UID，任务结束")
        return
    
    # 冷却相关变量
    processed_since_last_cooldown = 0
    total_processed = len(uid_list) - len(remaining_uids)  # 已经处理过的数量
    
    for i, uid in enumerate(remaining_uids):
        current_total_processed = total_processed + i + 1
        crawler_manager.logger.info(f"处理进度: {i+1}/{len(remaining_uids)} (总体: {current_total_processed}/{len(uid_list)})")
        
        # 直接处理一次，不再重试
        success = await process_single_uid(uid, crawler_manager)
        processed_since_last_cooldown += 1
        
        # 检查是否需要冷却
        if (config.ENABLE_COOLDOWN and 
            processed_since_last_cooldown >= config.COOLDOWN_TRIGGER_COUNT and 
            i < len(remaining_uids) - 1):  # 不是最后一个
            
            crawler_manager.logger.info(f"🎯 已处理 {processed_since_last_cooldown} 个UP主，触发冷却机制")
            await cooldown_with_display(
                config.COOLDOWN_DURATION, 
                crawler_manager.logger, 
                config.COOLDOWN_DISPLAY_INTERVAL
            )
            processed_since_last_cooldown = 0  # 重置计数器
        
        # 处理完成后等待随机时间，避免请求过于频繁
        elif i < len(remaining_uids) - 1:  # 不是最后一个且没有触发冷却
            delay_time = random.uniform(config.ACCOUNT_PROCESS_DELAY_MIN, config.ACCOUNT_PROCESS_DELAY_MAX)
            crawler_manager.logger.info(f"等待 {delay_time:.1f} 秒后处理下一个账号...")
            await asyncio.sleep(delay_time)
    
    crawler_manager.print_final_summary()


async def process_batch_bvs(bv_list: list, crawler_manager: BatchCrawlerManager):
    """批量处理BV号列表"""
    import random
    
    remaining_bvs = crawler_manager.initialize_batch(bv_list)
    
    if not remaining_bvs:
        crawler_manager.logger.info("没有需要处理的BV号，但仍需检查完成状态")
        # 即使没有待处理项目，也要调用最终总结来检查是否需要重置进度文件
        crawler_manager.print_final_summary()
        return
    
    # 冷却相关变量
    processed_since_last_cooldown = 0
    total_processed = len(bv_list) - len(remaining_bvs)  # 已经处理过的数量
    
    for i, bv_id in enumerate(remaining_bvs):
        current_total_processed = total_processed + i + 1
        crawler_manager.logger.info(f"处理进度: {i+1}/{len(remaining_bvs)} (总体: {current_total_processed}/{len(bv_list)})")
        
        # 直接处理一次，不再重试
        success = await process_single_bv(bv_id, crawler_manager)
        processed_since_last_cooldown += 1
        
        # 检查是否需要冷却
        if (config.ENABLE_COOLDOWN and 
            processed_since_last_cooldown >= config.COOLDOWN_TRIGGER_COUNT and 
            i < len(remaining_bvs) - 1):  # 不是最后一个
            
            crawler_manager.logger.info(f"🎯 已处理 {processed_since_last_cooldown} 个BV号，触发冷却机制")
            await cooldown_with_display(
                config.COOLDOWN_DURATION, 
                crawler_manager.logger, 
                config.COOLDOWN_DISPLAY_INTERVAL
            )
            processed_since_last_cooldown = 0  # 重置计数器
        
        # 处理完成后等待随机时间，避免请求过于频繁
        elif i < len(remaining_bvs) - 1:  # 不是最后一个且没有触发冷却
            delay_time = random.uniform(config.VIDEO_PROCESS_DELAY_MIN, config.VIDEO_PROCESS_DELAY_MAX)
            crawler_manager.logger.info(f"等待 {delay_time:.1f} 秒后处理下一个BV号...")
            await asyncio.sleep(delay_time)
    
    crawler_manager.print_final_summary()


async def main():
    print(
        """
# ⚠️声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：  
# ⚠️1. 不得用于任何商业用途。  
# ⚠️2. 使用时应遵守目标平台的使用条款和robots.txt规则。  
# ⚠️3. 不得进行大规模爬取或对平台造成运营干扰。  
# ⚠️4. 应合理控制请求频率，避免给目标平台带来不必要的负担。   
# ⚠️5. 不得用于任何非法或不当的用途。
# ⚠️  
# ⚠️详细许可条款请参阅项目根目录下的LICENSE文件。  
# ⚠️使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。
"""
    )

    # parse cmd
    cmd_arg.parse_cmd()

    # 判断是否传入了bili_uid_json参数
    bili_uid_json = getattr(config, 'BILI_UID_JSON', None)
    if config.PLATFORM == "bili" and bili_uid_json and os.path.exists(bili_uid_json):
        # 1. 先初始化数据库
        if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
            MYSQL_ACCOUNT_SAVE
        ]:
            await db.init_db()

        try:
            with open(bili_uid_json, 'r', encoding='utf-8') as f:
                uid_list = json.load(f)
            
            # 创建批量爬取管理器
            crawler_manager = BatchCrawlerManager()
            crawler_manager.logger.info(f"共读取到 {len(uid_list)} 个B站UID")
            
            # 批量处理UID
            await process_batch_uids(uid_list, crawler_manager)
            
        except Exception as e:
            print(f"批量处理UID过程中发生错误: {e}")
            raise
        finally:
            # 2. 循环结束后关闭数据库
            if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
                MYSQL_ACCOUNT_SAVE
            ]:
                await db.close()
        return  # 批量模式下，处理完直接return

    # 判断是否传入了bili_bv_json参数
    bili_bv_json = getattr(config, 'BILI_BV_JSON', None)
    if config.PLATFORM == "bili" and bili_bv_json and os.path.exists(bili_bv_json):
        # 1. 先初始化数据库
        if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
            MYSQL_ACCOUNT_SAVE
        ]:
            await db.init_db()

        try:
            # 智能解析JSON文件，支持简单和复杂格式
            bv_list = parse_bv_json_file_smart(bili_bv_json)

            if not bv_list:
                print("错误：JSON文件中没有找到有效的BV号")
                return

            # 创建批量爬取管理器
            crawler_manager = BatchCrawlerManager()
            crawler_manager.logger.info(f"共读取到 {len(bv_list)} 个B站BV号")

            # 批量处理BV号
            await process_batch_bvs(bv_list, crawler_manager)
            
        except Exception as e:
            print(f"批量处理BV号过程中发生错误: {e}")
            raise
        finally:
            # 2. 循环结束后关闭数据库
            if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
                MYSQL_ACCOUNT_SAVE
            ]:
                await db.close()
        return  # 批量模式下，处理完直接return

    # 判断是否传入了bili_bv_complex_json参数
    bili_bv_complex_json = getattr(config, 'BILI_BV_COMPLEX_JSON', None)
    print(f"🔍 检查复杂JSON配置: PLATFORM={config.PLATFORM}, bili_bv_complex_json={bili_bv_complex_json}")
    if bili_bv_complex_json:
        print(f"🔍 文件是否存在: {os.path.exists(bili_bv_complex_json)}")

    if config.PLATFORM == "bili" and bili_bv_complex_json and os.path.exists(bili_bv_complex_json):
        # 1. 先初始化数据库
        if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
            MYSQL_ACCOUNT_SAVE
        ]:
            await db.init_db()

        try:
            # 解析复杂格式JSON文件，提取BV号列表
            bv_list = parse_complex_bv_json_file(bili_bv_complex_json)

            if not bv_list:
                print("错误：复杂JSON文件中没有找到有效的BV号")
                return

            # 创建批量爬取管理器
            crawler_manager = BatchCrawlerManager()
            crawler_manager.logger.info(f"从复杂JSON文件共解析到 {len(bv_list)} 个B站BV号")

            # 批量处理BV号
            await process_batch_bvs(bv_list, crawler_manager)

        except Exception as e:
            print(f"批量处理复杂JSON BV号过程中发生错误: {e}")
            raise
        finally:
            # 2. 循环结束后关闭数据库
            if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
                MYSQL_ACCOUNT_SAVE
            ]:
                await db.close()
        return  # 批量模式下，处理完直接return

    # 判断是否传入了bili_bv_txt参数
    bili_bv_txt = getattr(config, 'BILI_BV_TXT', None)
    if config.PLATFORM == "bili" and bili_bv_txt and os.path.exists(bili_bv_txt):
        # 1. 先初始化数据库
        if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
            MYSQL_ACCOUNT_SAVE
        ]:
            await db.init_db()

        try:
            # 解析txt文件，提取BV号列表
            bv_list = parse_bv_txt_file(bili_bv_txt)
            
            if not bv_list:
                print("错误：txt文件中没有找到有效的BV号")
                return
            
            # 创建批量爬取管理器
            crawler_manager = BatchCrawlerManager()
            crawler_manager.logger.info(f"从txt文件共解析到 {len(bv_list)} 个B站BV号")
            
            # 批量处理BV号
            await process_batch_bvs(bv_list, crawler_manager)
            
        except Exception as e:
            print(f"批量处理txt文件过程中发生错误: {e}")
            raise
        finally:
            # 2. 循环结束后关闭数据库
            if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
                MYSQL_ACCOUNT_SAVE
            ]:
                await db.close()
        return  # 批量模式下，处理完直接return

    # 下面是原有单次爬取逻辑
    if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
        MYSQL_ACCOUNT_SAVE
    ]:
        await db.init_db()

    crawler = CrawlerFactory.create_crawler(platform=config.PLATFORM)
    await crawler.async_initialize()
    await crawler.start()

    if config.SAVE_DATA_OPTION == "db" or config.ACCOUNT_POOL_SAVE_TYPE in [
        MYSQL_ACCOUNT_SAVE
    ]:
        await db.close()


if __name__ == "__main__":
    try:
        # asyncio.run(main())
        asyncio.get_event_loop().run_until_complete(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit()
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)
