#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空B站评论数据脚本
用于在每次爬取前清空数据库中的B站评论数据，确保只保留最新的评论
"""

import asyncio
import sys
import os
from datetime import datetime

def setup_python_path():
    """设置Python路径，确保能找到MediaCrawlerPro-Python模块"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 可能的MediaCrawlerPro-Python路径
    possible_paths = [
        current_dir,  # 当前目录（如果脚本在MediaCrawlerPro-Python内）
        os.path.join(current_dir, 'MediaCrawlerPro-Python'),  # 子目录
        os.path.join(os.path.dirname(current_dir), 'MediaCrawlerPro-Python'),  # 兄弟目录
    ]
    
    for path in possible_paths:
        if os.path.exists(os.path.join(path, 'async_db.py')):
            if path not in sys.path:
                sys.path.insert(0, path)
            print(f"✅ 找到MediaCrawlerPro-Python路径: {path}")
            return path
    
    return None

# 设置路径
project_path = setup_python_path()
if not project_path:
    print("❌ 无法找到MediaCrawlerPro-Python目录")
    print("请确保脚本在正确的位置运行")
    sys.exit(1)

try:
    import aiomysql
    from async_db import AsyncMysqlDB
    from config import db_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print(f"当前Python路径: {sys.path}")
    print("请检查MediaCrawlerPro-Python目录结构")
    sys.exit(1)

class BilibiliCommentCleaner:
    def __init__(self):
        self.db_conn = None
        self.pool = None
        
    async def init_db(self):
        """初始化数据库连接"""
        try:
            # 创建连接池
            self.pool = await aiomysql.create_pool(
                host=db_config.RELATION_DB_HOST,
                port=db_config.RELATION_DB_PORT,
                user=db_config.RELATION_DB_USER,
                password=db_config.RELATION_DB_PWD,
                db=db_config.RELATION_DB_NAME,
                autocommit=True,
            )
            # 创建AsyncMysqlDB实例
            self.db_conn = AsyncMysqlDB(self.pool)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    async def close_db(self):
        """关闭数据库连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            print("✅ 数据库连接已关闭")
    
    async def get_comment_count(self):
        """获取当前评论数量"""
        try:
            sql = "SELECT COUNT(*) as count FROM bilibili_video_comment"
            result = await self.db_conn.query(sql)
            if result:
                return result[0]['count']
            return 0
        except Exception as e:
            print(f"❌ 获取评论数量失败: {e}")
            return 0
    
    async def get_comment_stats(self):
        """获取评论统计信息"""
        try:
            # 获取总数
            total_sql = "SELECT COUNT(*) as total FROM bilibili_video_comment"
            total_result = await self.db_conn.query(total_sql)
            total_count = total_result[0]['total'] if total_result else 0
            
            # 获取最早和最新的评论时间
            time_sql = """
            SELECT 
                MIN(create_time) as earliest_time,
                MAX(create_time) as latest_time,
                MIN(add_ts) as earliest_add_ts,
                MAX(add_ts) as latest_add_ts
            FROM bilibili_video_comment
            """
            time_result = await self.db_conn.query(time_sql)
            
            # 获取涉及的视频数量
            video_sql = "SELECT COUNT(DISTINCT video_id) as video_count FROM bilibili_video_comment"
            video_result = await self.db_conn.query(video_sql)
            video_count = video_result[0]['video_count'] if video_result else 0
            
            stats = {
                'total_count': total_count,
                'video_count': video_count,
                'earliest_time': time_result[0]['earliest_time'] if time_result and time_result[0]['earliest_time'] else None,
                'latest_time': time_result[0]['latest_time'] if time_result and time_result[0]['latest_time'] else None,
                'earliest_add_ts': time_result[0]['earliest_add_ts'] if time_result and time_result[0]['earliest_add_ts'] else None,
                'latest_add_ts': time_result[0]['latest_add_ts'] if time_result and time_result[0]['latest_add_ts'] else None,
            }
            
            return stats
        except Exception as e:
            print(f"❌ 获取评论统计信息失败: {e}")
            return None
    
    def format_timestamp(self, timestamp):
        """格式化时间戳"""
        if timestamp:
            try:
                return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            except:
                return str(timestamp)
        return "未知"
    
    async def clear_comments(self):
        """清空B站评论数据"""
        try:
            sql = "DELETE FROM bilibili_video_comment"
            result = await self.db_conn.execute(sql)
            print(f"✅ 成功清空B站评论数据，影响行数: {result}")
            return True
        except Exception as e:
            print(f"❌ 清空评论数据失败: {e}")
            return False
    
    async def backup_comments(self, backup_file=None):
        """备份评论数据到文件"""
        if not backup_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"bilibili_comments_backup_{timestamp}.sql"
        
        try:
            # 获取所有评论数据
            sql = "SELECT * FROM bilibili_video_comment"
            comments = await self.db_conn.query(sql)
            
            if not comments:
                print("⚠️  没有评论数据需要备份")
                return True
            
            # 生成备份SQL
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write("-- B站评论数据备份\n")
                f.write(f"-- 备份时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"-- 总记录数: {len(comments)}\n\n")
                
                # 写入表结构（简化版）
                f.write("-- 如需恢复，请先确保表结构存在\n\n")
                
                # 写入数据
                for comment in comments:
                    # 构建INSERT语句
                    columns = list(comment.keys())
                    values = []
                    for value in comment.values():
                        if value is None:
                            values.append('NULL')
                        elif isinstance(value, str):
                            # 转义单引号
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        else:
                            values.append(str(value))
                    
                    insert_sql = f"INSERT INTO bilibili_video_comment ({', '.join(columns)}) VALUES ({', '.join(values)});\n"
                    f.write(insert_sql)
            
            print(f"✅ 评论数据已备份到: {backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ 备份评论数据失败: {e}")
            return False

async def main():
    """主函数"""
    print("🚀 B站评论数据清理工具")
    print("=" * 50)
    
    cleaner = BilibiliCommentCleaner()
    
    # 初始化数据库连接
    if not await cleaner.init_db():
        return
    
    try:
        # 获取当前评论统计信息
        print("📊 正在获取当前评论数据统计...")
        stats = await cleaner.get_comment_stats()
        
        if not stats or stats['total_count'] == 0:
            print("ℹ️  数据库中没有B站评论数据，无需清理")
            return
        
        # 显示统计信息
        print(f"\n📈 当前B站评论数据统计:")
        print(f"   总评论数: {stats['total_count']:,}")
        print(f"   涉及视频数: {stats['video_count']:,}")
        if stats['earliest_time'] and stats['latest_time']:
            print(f"   评论时间范围: {cleaner.format_timestamp(stats['earliest_time'])} ~ {cleaner.format_timestamp(stats['latest_time'])}")
        if stats['earliest_add_ts'] and stats['latest_add_ts']:
            print(f"   入库时间范围: {cleaner.format_timestamp(stats['earliest_add_ts'])} ~ {cleaner.format_timestamp(stats['latest_add_ts'])}")
        
        # 询问是否需要备份
        print(f"\n⚠️  即将清空 {stats['total_count']:,} 条B站评论数据！")
        backup_choice = input("是否需要先备份数据？(y/N): ").strip().lower()
        
        if backup_choice in ['y', 'yes']:
            print("\n💾 正在备份评论数据...")
            if await cleaner.backup_comments():
                print("✅ 数据备份完成")
            else:
                print("❌ 数据备份失败，是否继续清理？")
                continue_choice = input("继续清理？(y/N): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    print("🚫 操作已取消")
                    return
        
        # 最终确认
        print(f"\n🔥 最终确认：即将删除 {stats['total_count']:,} 条B站评论数据")
        print("⚠️  此操作不可逆！请确保您已经备份了重要数据")
        confirm = input("请输入 'DELETE' 来确认删除操作: ").strip()
        
        if confirm != 'DELETE':
            print("🚫 操作已取消")
            return
        
        # 执行清理
        print("\n🧹 正在清理B站评论数据...")
        if await cleaner.clear_comments():
            print("✅ B站评论数据清理完成！")
            
            # 验证清理结果
            remaining_count = await cleaner.get_comment_count()
            if remaining_count == 0:
                print("✅ 验证通过：数据库中已无B站评论数据")
            else:
                print(f"⚠️  警告：数据库中仍有 {remaining_count} 条评论数据")
        else:
            print("❌ 清理操作失败")
    
    except KeyboardInterrupt:
        print("\n🚫 操作被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    finally:
        await cleaner.close_db()

if __name__ == "__main__":
    print("B站评论数据清理脚本")
    print("⚠️  警告：此脚本将清空数据库中的所有B站评论数据")
    print("🔧 使用前请确保已正确配置数据库连接")
    print("-" * 50)
    
    asyncio.run(main()) 