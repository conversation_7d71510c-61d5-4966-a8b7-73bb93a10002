#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP API 使用示例
"""

import requests
import json
import time

# 服务器地址
BASE_URL = "http://localhost:8080"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def create_bv_complex_json_task():
    """创建复杂JSON格式BV号爬取任务"""
    print("📝 创建复杂JSON格式BV号爬取任务...")
    
    task_data = {
        "platform": "bili",
        "type": "detail",
        "bv_complex_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"
    }
    
    response = requests.post(
        f"{BASE_URL}/tasks",
        headers={"Content-Type": "application/json"},
        data=json.dumps(task_data)
    )
    
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {result}")
    
    if response.status_code == 201:
        task_id = result['task_id']
        print(f"✅ 任务创建成功，任务ID: {task_id}")
        return task_id
    else:
        print("❌ 任务创建失败")
        return None

def create_simple_json_task():
    """创建简单JSON格式BV号爬取任务"""
    print("📝 创建简单JSON格式BV号爬取任务...")
    
    task_data = {
        "platform": "bili",
        "type": "detail",
        "bv_json_file": "/Users/<USER>/Desktop/MediaCrawlerPro/bv_list.json"
    }
    
    response = requests.post(
        f"{BASE_URL}/tasks",
        headers={"Content-Type": "application/json"},
        data=json.dumps(task_data)
    )
    
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {result}")
    
    if response.status_code == 201:
        task_id = result['task_id']
        print(f"✅ 任务创建成功，任务ID: {task_id}")
        return task_id
    else:
        print("❌ 任务创建失败")
        return None

def create_txt_task():
    """创建TXT文件BV号爬取任务"""
    print("📝 创建TXT文件BV号爬取任务...")
    
    task_data = {
        "platform": "bili",
        "type": "detail",
        "bv_txt_file": "/Users/<USER>/Desktop/MediaCrawlerPro/齐鸟收录后40.txt"
    }
    
    response = requests.post(
        f"{BASE_URL}/tasks",
        headers={"Content-Type": "application/json"},
        data=json.dumps(task_data)
    )
    
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {result}")
    
    if response.status_code == 201:
        task_id = result['task_id']
        print(f"✅ 任务创建成功，任务ID: {task_id}")
        return task_id
    else:
        print("❌ 任务创建失败")
        return None

def create_search_task():
    """创建关键词搜索任务"""
    print("📝 创建关键词搜索任务...")
    
    task_data = {
        "platform": "bili",
        "type": "search",
        "keywords": "deepseek,chatgpt"
    }
    
    response = requests.post(
        f"{BASE_URL}/tasks",
        headers={"Content-Type": "application/json"},
        data=json.dumps(task_data)
    )
    
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {result}")
    
    if response.status_code == 201:
        task_id = result['task_id']
        print(f"✅ 任务创建成功，任务ID: {task_id}")
        return task_id
    else:
        print("❌ 任务创建失败")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    print(f"📊 获取任务状态: {task_id}")
    
    response = requests.get(f"{BASE_URL}/tasks/{task_id}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"任务状态: {result['status']}")
        print(f"创建时间: {result['created_at']}")
        if result.get('started_at'):
            print(f"开始时间: {result['started_at']}")
        if result.get('completed_at'):
            print(f"完成时间: {result['completed_at']}")
        if result.get('error'):
            print(f"错误信息: {result['error']}")
        return result
    else:
        print("❌ 获取任务状态失败")
        return None

def list_all_tasks():
    """列出所有任务"""
    print("📋 列出所有任务...")
    
    response = requests.get(f"{BASE_URL}/tasks")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"总任务数: {result['total']}")
        for task in result['tasks']:
            print(f"- 任务ID: {task['id']}, 状态: {task['status']}")
        return result
    else:
        print("❌ 获取任务列表失败")
        return None

def monitor_task(task_id, max_wait_time=300):
    """监控任务执行"""
    print(f"👀 监控任务执行: {task_id}")
    
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        task = get_task_status(task_id)
        if not task:
            break
        
        status = task['status']
        if status in ['completed', 'failed', 'cancelled']:
            print(f"🎯 任务最终状态: {status}")
            if status == 'failed' and task.get('error'):
                print(f"❌ 错误信息: {task['error']}")
            break
        
        print(f"⏳ 任务状态: {status}, 等待中...")
        time.sleep(10)  # 每10秒检查一次
    else:
        print("⏰ 监控超时")

def main():
    """主函数 - 演示API使用"""
    print("🚀 MediaCrawlerPro HTTP API 使用示例\n")
    
    # 1. 健康检查
    test_health_check()
    
    # 2. 创建任务（选择一种）
    print("选择要测试的任务类型:")
    print("1. 复杂JSON格式BV号爬取")
    print("2. 简单JSON格式BV号爬取")
    print("3. TXT文件BV号爬取")
    print("4. 关键词搜索")
    print("5. 列出所有任务")
    
    choice = input("请输入选择 (1-5): ").strip()
    
    task_id = None
    if choice == "1":
        task_id = create_bv_complex_json_task()
    elif choice == "2":
        task_id = create_simple_json_task()
    elif choice == "3":
        task_id = create_txt_task()
    elif choice == "4":
        task_id = create_search_task()
    elif choice == "5":
        list_all_tasks()
        return
    else:
        print("❌ 无效选择")
        return
    
    if task_id:
        print()
        # 3. 监控任务执行
        monitor_task(task_id)
        
        print()
        # 4. 列出所有任务
        list_all_tasks()

if __name__ == '__main__':
    main()
