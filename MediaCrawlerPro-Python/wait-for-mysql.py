#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
等待MySQL数据库完全启动并初始化完成的脚本
"""

import time
import sys
import os
import pymysql
from pymysql import Error

def wait_for_mysql():
    """等待MySQL数据库启动并可连接"""
    max_attempts = 30  # 最多等待30次，每次2秒，总共60秒
    attempt = 0
    
    # 从环境变量获取数据库连接信息
    host = os.getenv('RELATION_DB_HOST', 'mysql_db')
    port = int(os.getenv('RELATION_DB_PORT', '3306'))
    user = os.getenv('RELATION_DB_USER', 'root')
    password = os.getenv('RELATION_DB_PWD', '123456')
    database = os.getenv('RELATION_DB_NAME', 'media_crawler')
    
    print(f"等待MySQL数据库启动... (host={host}, port={port}, database={database})")
    
    while attempt < max_attempts:
        try:
            # 尝试连接数据库
            connection = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database,
                charset='utf8mb4',
                connect_timeout=5
            )
            
            # 检查表是否存在（确保初始化脚本已执行）
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if len(tables) > 0:
                    print(f"✅ MySQL数据库已启动并初始化完成！共找到 {len(tables)} 个表")
                    connection.close()
                    return True
                else:
                    print(f"⏳ 数据库已连接，但表尚未创建，继续等待... (尝试 {attempt + 1}/{max_attempts})")
            
            connection.close()
            
        except Error as e:
            print(f"⏳ 等待数据库启动... (尝试 {attempt + 1}/{max_attempts}) - {str(e)}")
        except Exception as e:
            print(f"⏳ 连接数据库时出错... (尝试 {attempt + 1}/{max_attempts}) - {str(e)}")
        
        attempt += 1
        time.sleep(2)
    
    print("❌ 等待数据库启动超时！")
    return False

if __name__ == "__main__":
    if wait_for_mysql():
        print("🚀 数据库准备就绪，可以启动应用程序")
        sys.exit(0)
    else:
        print("💥 数据库启动失败，退出")
        sys.exit(1)
