<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaCrawlerPro Web 控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        select, input, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .task-item {
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .task-id {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        
        .task-status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .task-status.pending { background: #fff3cd; color: #856404; }
        .task-status.running { background: #cce5ff; color: #004085; }
        .task-status.completed { background: #d4edda; color: #155724; }
        .task-status.failed { background: #f8d7da; color: #721c24; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MediaCrawlerPro Web 控制台</h1>
            <p>服务状态: <span id="serverStatus" class="status offline">离线</span></p>
        </div>
        
        <div class="card">
            <h2>创建爬取任务</h2>
            <form id="taskForm">
                <div class="form-group">
                    <label for="platform">平台:</label>
                    <select id="platform" name="platform">
                        <option value="bili">B站 (bilibili)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="type">任务类型:</label>
                    <select id="type" name="type">
                        <option value="detail">视频详情爬取</option>
                        <option value="search">关键词搜索</option>
                        <option value="creator">UP主数据爬取</option>
                    </select>
                </div>
                
                <div id="fileGroup" class="form-group">
                    <label for="fileType">文件类型:</label>
                    <select id="fileType" name="fileType">
                        <option value="bv_complex_json_file">复杂JSON格式 (推荐)</option>
                        <option value="bv_json_file">简单JSON格式</option>
                        <option value="bv_txt_file">TXT文件</option>
                        <option value="uid_json_file">UID JSON文件</option>
                    </select>
                </div>
                
                <div id="filePathGroup" class="form-group">
                    <label for="filePath">文件路径:</label>
                    <input type="text" id="filePath" name="filePath" 
                           placeholder="/Users/<USER>/Desktop/MediaCrawlerPro/bv.json">
                </div>
                
                <div id="keywordsGroup" class="form-group" style="display: none;">
                    <label for="keywords">搜索关键词:</label>
                    <input type="text" id="keywords" name="keywords" 
                           placeholder="deepseek,chatgpt">
                </div>
                
                <button type="submit">创建任务</button>
            </form>
        </div>
        
        <div class="card">
            <h2>任务列表 <button onclick="refreshTasks()">刷新</button></h2>
            <div id="taskList" class="task-list">
                <p>加载中...</p>
            </div>
        </div>
        
        <div class="card">
            <h2>操作日志</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = '在线';
                    document.getElementById('serverStatus').className = 'status online';
                    return true;
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '离线';
                document.getElementById('serverStatus').className = 'status offline';
                return false;
            }
        }
        
        // 添加日志
        function addLog(message) {
            const log = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `[${time}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        // 任务类型变化处理
        document.getElementById('type').addEventListener('change', function() {
            const type = this.value;
            const fileGroup = document.getElementById('fileGroup');
            const filePathGroup = document.getElementById('filePathGroup');
            const keywordsGroup = document.getElementById('keywordsGroup');
            
            if (type === 'search') {
                fileGroup.style.display = 'none';
                filePathGroup.style.display = 'none';
                keywordsGroup.style.display = 'block';
            } else {
                fileGroup.style.display = 'block';
                filePathGroup.style.display = 'block';
                keywordsGroup.style.display = 'none';
            }
        });
        
        // 提交表单
        document.getElementById('taskForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const taskData = {
                platform: formData.get('platform'),
                type: formData.get('type')
            };
            
            if (taskData.type === 'search') {
                taskData.keywords = formData.get('keywords');
            } else {
                const fileType = formData.get('fileType');
                const filePath = formData.get('filePath');
                taskData[fileType] = filePath;
            }
            
            try {
                addLog(`创建任务: ${JSON.stringify(taskData)}`);
                
                const response = await fetch(`${API_BASE}/tasks`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    addLog(`✅ 任务创建成功: ${result.task_id}`);
                    refreshTasks();
                } else {
                    addLog(`❌ 任务创建失败: ${result.error}`);
                }
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`);
            }
        });
        
        // 刷新任务列表
        async function refreshTasks() {
            try {
                const response = await fetch(`${API_BASE}/tasks`);
                const result = await response.json();
                
                const taskList = document.getElementById('taskList');
                
                if (result.tasks.length === 0) {
                    taskList.innerHTML = '<p>暂无任务</p>';
                    return;
                }
                
                taskList.innerHTML = result.tasks.map(task => `
                    <div class="task-item">
                        <div class="task-header">
                            <span class="task-id">${task.id}</span>
                            <span class="task-status ${task.status}">${task.status}</span>
                        </div>
                        <div>
                            <strong>平台:</strong> ${task.config.platform} | 
                            <strong>类型:</strong> ${task.config.type}
                        </div>
                        <div>
                            <strong>创建时间:</strong> ${new Date(task.created_at).toLocaleString()}
                        </div>
                        ${task.error ? `<div style="color: red;"><strong>错误:</strong> ${task.error}</div>` : ''}
                    </div>
                `).join('');
                
            } catch (error) {
                addLog(`❌ 获取任务列表失败: ${error.message}`);
            }
        }
        
        // 初始化
        async function init() {
            addLog('🚀 Web控制台启动');
            
            const isOnline = await checkServerStatus();
            if (isOnline) {
                addLog('✅ 服务器连接成功');
                refreshTasks();
            } else {
                addLog('❌ 服务器连接失败，请确保HTTP中间件已启动');
            }
        }
        
        // 定期检查服务器状态和刷新任务
        setInterval(checkServerStatus, 30000);
        setInterval(refreshTasks, 10000);
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
