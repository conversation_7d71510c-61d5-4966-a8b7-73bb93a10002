#!/bin/bash

# MediaCrawlerPro Docker 部署脚本
# 使用方法: ./docker_deploy.sh [start|stop|restart|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_blue() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查Docker和Docker Compose是否安装
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    if [ ! -f "docker-compose.yaml" ]; then
        log_error "docker-compose.yaml 文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 文件不存在"
        exit 1
    fi
    
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    log_info "文件检查通过"
}

# 创建必要的目录和文件
prepare_environment() {
    log_info "准备环境..."
    
    # 创建日志目录
    mkdir -p logs/bili logs/xhs
    
    # BV文件仅用于测试，不需要预创建
    
    # 如果进度文件不存在，创建空文件
    if [ ! -f "crawler_progress.json" ]; then
        log_info "创建空的进度文件"
        echo '{}' > crawler_progress.json
    fi
    
    log_info "环境准备完成"
}

# 启动服务
start_services() {
    log_info "启动 MediaCrawlerPro 服务..."
    
    # 构建并启动服务
    docker-compose up -d --build
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "服务启动成功！"
        log_blue "HTTP中间件地址: http://localhost:8080"
        log_blue "健康检查: http://localhost:8080/health"
        log_blue "MySQL端口: 3307"
        log_blue "Redis端口: 6378"
        log_blue "签名服务端口: 8989"
        echo ""
        log_blue "API使用示例:"
        echo "curl -X POST http://localhost:8080/tasks \\"
        echo "  -H \"Content-Type: application/json\" \\"
        echo "  -d '{\"platform\": \"bili\", \"type\": \"detail\", \"bv_complex_json_file\": \"/app/bv.json\"}'"
    else
        log_error "服务启动失败，请查看日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止 MediaCrawlerPro 服务..."
    docker-compose down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 MediaCrawlerPro 服务..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f
}

# 查看状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    echo ""
    log_info "容器资源使用情况:"
    docker stats --no-stream $(docker-compose ps -q) 2>/dev/null || log_warn "无运行中的容器"
}

# 清理环境
cleanup() {
    log_info "清理 Docker 环境..."
    docker-compose down -v
    docker system prune -f
    log_info "清理完成"
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_dependencies
            check_files
            prepare_environment
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup
            ;;
        *)
            echo "使用方法: $0 [start|stop|restart|logs|status|cleanup]"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动服务 (默认)"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  logs    - 查看日志"
            echo "  status  - 查看状态"
            echo "  cleanup - 清理环境"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
