#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度管理工具
用于查看、重置、清理批量爬取的进度状态
"""

import json
import os
import argparse
from datetime import datetime
import config


def load_progress(progress_file: str = None) -> dict:
    """加载进度文件"""
    progress_file = progress_file or config.PROGRESS_FILE_PATH
    if os.path.exists(progress_file):
        with open(progress_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}


def save_progress(data: dict, progress_file: str = None):
    """保存进度文件"""
    progress_file = progress_file or config.PROGRESS_FILE_PATH
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def show_status(progress_file: str = None):
    """显示当前状态"""
    progress_data = load_progress(progress_file)
    
    if not progress_data:
        print("❌ 未找到进度文件")
        return
    
    print("=== 批量爬取进度状态 ===")
    print(f"开始时间: {progress_data.get('start_time', 'N/A')}")
    print(f"最后更新: {progress_data.get('last_update', 'N/A')}")
    print(f"总UID数量: {progress_data.get('total_uids', 0)}")
    
    stats = progress_data.get('statistics', {})
    print(f"\n📊 统计信息:")
    print(f"  ✅ 成功: {stats.get('success_count', 0)}")
    print(f"  ❌ 失败: {stats.get('failed_count', 0)}")
    print(f"  ⏭️ 跳过: {stats.get('skipped_count', 0)}")
    
    current = progress_data.get('current_processing')
    if current:
        print(f"\n🔄 当前处理: {current}")
    
    processed = progress_data.get('processed_uids', [])
    failed = progress_data.get('failed_uids', [])
    skipped = progress_data.get('skipped_uids', [])
    
    print(f"\n📝 详细信息:")
    print(f"  已处理UID数量: {len(processed)}")
    if processed:
        print(f"  最近处理的UID: {processed[-5:]}")  # 显示最后5个
    
    if failed:
        print(f"  失败UID数量: {len(failed)}")
        print(f"  失败的UID: {failed}")
    
    if skipped:
        print(f"  跳过UID数量: {len(skipped)}")
        print(f"  跳过的UID: {skipped}")
    
    retry_count = progress_data.get('retry_count', {})
    if retry_count:
        print(f"\n🔄 重试计数:")
        for uid, count in retry_count.items():
            print(f"  {uid}: {count}次")


def reset_failed(progress_file: str = None):
    """重置失败的UID，允许重新处理"""
    progress_data = load_progress(progress_file)
    
    if not progress_data:
        print("❌ 未找到进度文件")
        return
    
    failed_uids = progress_data.get('failed_uids', [])
    if not failed_uids:
        print("✅ 没有失败的UID需要重置")
        return
    
    print(f"🔄 重置 {len(failed_uids)} 个失败的UID:")
    for uid in failed_uids:
        print(f"  - {uid}")
    
    # 清除失败记录
    progress_data['failed_uids'] = []
    progress_data['retry_count'] = {}
    progress_data['statistics']['failed_count'] = 0
    progress_data['last_update'] = datetime.now().isoformat()
    
    save_progress(progress_data, progress_file)
    print("✅ 失败UID已重置，可以重新处理")


def reset_all(progress_file: str = None):
    """重置所有进度"""
    progress_file = progress_file or config.PROGRESS_FILE_PATH
    
    if os.path.exists(progress_file):
        # 备份当前进度文件
        backup_file = f"{progress_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(progress_file, backup_file)
        print(f"📁 当前进度已备份至: {backup_file}")
        
        # 删除进度文件
        os.remove(progress_file)
        print("🗑️ 进度文件已删除")
    else:
        print("❌ 未找到进度文件")


def remove_uid(uid: str, progress_file: str = None):
    """从所有列表中移除指定的UID"""
    progress_data = load_progress(progress_file)
    
    if not progress_data:
        print("❌ 未找到进度文件")
        return
    
    removed = False
    
    # 从各个列表中移除
    if uid in progress_data.get('processed_uids', []):
        progress_data['processed_uids'].remove(uid)
        progress_data['statistics']['success_count'] -= 1
        removed = True
        print(f"✅ 从已处理列表中移除 UID: {uid}")
    
    if uid in progress_data.get('failed_uids', []):
        progress_data['failed_uids'].remove(uid)
        progress_data['statistics']['failed_count'] -= 1
        removed = True
        print(f"❌ 从失败列表中移除 UID: {uid}")
    
    if uid in progress_data.get('skipped_uids', []):
        progress_data['skipped_uids'].remove(uid)
        progress_data['statistics']['skipped_count'] -= 1
        removed = True
        print(f"⏭️ 从跳过列表中移除 UID: {uid}")
    
    if uid in progress_data.get('retry_count', {}):
        del progress_data['retry_count'][uid]
        removed = True
        print(f"🔄 清除 UID {uid} 的重试计数")
    
    if progress_data.get('current_processing') == uid:
        progress_data['current_processing'] = None
        removed = True
        print(f"🔄 清除当前处理状态中的 UID: {uid}")
    
    if removed:
        progress_data['last_update'] = datetime.now().isoformat()
        save_progress(progress_data, progress_file)
        print(f"✅ UID {uid} 已完全移除，可以重新处理")
    else:
        print(f"ℹ️ UID {uid} 未在任何列表中找到")


def main():
    parser = argparse.ArgumentParser(description="批量爬取进度管理工具")
    parser.add_argument('--progress-file', type=str, help='指定进度文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 状态查看
    subparsers.add_parser('status', help='查看当前进度状态')
    
    # 重置失败
    subparsers.add_parser('reset-failed', help='重置失败的UID，允许重新处理')
    
    # 重置所有
    subparsers.add_parser('reset-all', help='重置所有进度（会备份当前进度）')
    
    # 移除UID
    remove_parser = subparsers.add_parser('remove-uid', help='从所有列表中移除指定UID')
    remove_parser.add_argument('uid', type=str, help='要移除的UID')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'status':
        show_status(args.progress_file)
    elif args.command == 'reset-failed':
        reset_failed(args.progress_file)
    elif args.command == 'reset-all':
        reset_all(args.progress_file)
    elif args.command == 'remove-uid':
        remove_uid(args.uid, args.progress_file)


if __name__ == "__main__":
    main() 