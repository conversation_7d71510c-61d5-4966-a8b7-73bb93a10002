# 关键词自动爬虫使用说明

## 概述

`keyword_auto_crawler.py` 是一个关键词自动爬虫程序，能够从指定文件中读取关键词列表，逐个进行爬取。支持多种文件格式和平台。

## 功能特点

- 🔄 **自动化处理**: 逐个处理关键词列表，无需手动干预
- 📁 **多格式支持**: 支持 txt、json、csv 三种文件格式
- 🎯 **多平台支持**: 支持 B站、小红书、抖音、快手、微博、贴吧、知乎
- ⏱️ **智能等待**: 可配置关键词间等待时间，避免频繁请求
- 📊 **实时统计**: 显示处理进度和成功率统计
- 🔧 **跨平台**: 支持 Windows、macOS、Linux
- 📝 **详细日志**: 自动记录运行日志到文件

## 安装要求

确保您已经安装了 MediaCrawlerPro 的所有依赖：

```bash
pip install -r requirements.txt
```

## 文件格式说明

### 1. TXT 格式 (推荐)
每行一个关键词：
```
deepseek
chatgpt
人工智能
机器学习
```

### 2. JSON 格式
字符串数组格式：
```json
[
    "deepseek",
    "chatgpt", 
    "人工智能",
    "机器学习"
]
```

### 3. CSV 格式
第一列为关键词：
```csv
关键词,备注
deepseek,AI模型
chatgpt,聊天机器人
人工智能,技术领域
```

## 使用方法

### 基本用法

```bash
# 使用默认设置爬取（B站搜索，等待5分钟）
python3 keyword_auto_crawler.py keywords.txt
```

### 高级用法

```bash
# 爬取小红书内容
python3 keyword_auto_crawler.py keywords.txt --platform xhs

# 设置等待时间为10分钟（600秒）
python3 keyword_auto_crawler.py keywords.txt --wait-time 600

# 爬取抖音内容，等待时间2分钟
python3 keyword_auto_crawler.py keywords.json --platform dy --wait-time 120

# 爬取详情页内容
python3 keyword_auto_crawler.py keywords.txt --type detail

# 组合使用多个参数
python3 keyword_auto_crawler.py keywords.csv --platform bili --type search --wait-time 300
```

## 参数说明

| 参数 | 说明 | 可选值 | 默认值 |
|------|------|--------|--------|
| `keywords_file` | 关键词文件路径 | 文件路径 | 必填 |
| `--platform` | 爬取平台 | bili, xhs, dy, ks, wb, tieba, zhihu | bili |
| `--type` | 爬取类型 | search, detail, creator, homefeed | search |
| `--wait-time` | 等待时间（秒） | 正整数 | 300 |
| `--max-notes` | 每个关键词最大爬取数量 | 正整数 | 100 |

## 平台说明

| 平台代码 | 平台名称 | 说明 |
|----------|----------|------|
| bili | B站 | 哔哩哔哩视频平台 |
| xhs | 小红书 | 生活方式分享平台 |
| dy | 抖音 | 短视频平台 |
| ks | 快手 | 短视频平台 |
| wb | 微博 | 社交媒体平台 |
| tieba | 贴吧 | 百度贴吧 |
| zhihu | 知乎 | 问答社区 |

## 运行示例

### 示例1：爬取B站AI相关内容
```bash
python3 keyword_auto_crawler.py ai_keywords.txt --platform bili --wait-time 300
```

### 示例2：爬取小红书美食内容
```bash
python3 keyword_auto_crawler.py food_keywords.json --platform xhs --wait-time 600
```

### 示例3：快速测试（无等待）
```bash
python3 keyword_auto_crawler.py test_keywords.txt --wait-time 0
```

## 日志文件

程序运行时会生成 `keyword_auto_crawler.log` 日志文件，包含：
- 程序启动信息
- 关键词处理进度
- 成功/失败统计
- 错误信息记录

## 注意事项

1. **合规使用**: 请遵守各平台的使用条款和 robots.txt 规则
2. **频率控制**: 建议设置合理的等待时间，避免对平台造成压力
3. **数据存储**: 默认将数据保存到数据库，确保数据库配置正确
4. **网络环境**: 确保网络连接稳定，某些平台可能需要代理
5. **账号登录**: 某些平台需要登录状态，请提前配置好账号信息

## 停止程序

- **Windows**: 按 `Ctrl+C`
- **macOS/Linux**: 按 `Ctrl+C` 或 `Command+C`

程序会优雅地停止当前任务并显示统计信息。

## 故障排除

### 常见问题

1. **文件不存在错误**
   - 检查关键词文件路径是否正确
   - 确保文件编码为 UTF-8

2. **Python命令找不到**
   - 确保 Python 已正确安装
   - 尝试使用 `python` 或 `python3` 命令

3. **爬取失败**
   - 检查网络连接
   - 确认平台账号登录状态
   - 查看日志文件获取详细错误信息

4. **编码问题**
   - 确保关键词文件使用 UTF-8 编码
   - Windows 用户可能需要设置控制台编码

### 获取帮助

```bash
python3 keyword_auto_crawler.py --help
```

## 更新日志

- v1.0.0: 初始版本，支持基本的关键词自动爬取功能
- 支持多种文件格式和平台
- 添加详细的日志记录和统计功能 