#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速清空数据库中所有数据脚本（全面版）
用于自动化场景，清空视频信息、评论等所有相关数据
支持多平台数据清理
"""

import asyncio
import sys
import os
from datetime import datetime
import argparse

def setup_python_path():
    """设置Python路径，确保能找到MediaCrawlerPro-Python模块"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 可能的MediaCrawlerPro-Python路径
    possible_paths = [
        current_dir,  # 当前目录（如果脚本在MediaCrawlerPro-Python内）
        os.path.join(current_dir, 'MediaCrawlerPro-Python'),  # 子目录
        os.path.join(os.path.dirname(current_dir), 'MediaCrawlerPro-Python'),  # 兄弟目录
    ]
    
    for path in possible_paths:
        if os.path.exists(os.path.join(path, 'async_db.py')):
            if path not in sys.path:
                sys.path.insert(0, path)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 找到MediaCrawlerPro-Python路径: {path}")
            return path
    
    return None

# 设置路径
project_path = setup_python_path()
if not project_path:
    print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 无法找到MediaCrawlerPro-Python目录")
    print("请确保脚本在正确的位置运行")
    sys.exit(1)

try:
    import aiomysql
    from async_db import AsyncMysqlDB
    from config import db_config
except ImportError as e:
    print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 导入模块失败: {e}")
    print(f"当前Python路径: {sys.path}")
    print("请检查MediaCrawlerPro-Python目录结构")
    sys.exit(1)

# 定义各平台的数据表
PLATFORM_TABLES = {
    'bilibili': {
        'video': 'bilibili_video',
        'comment': 'bilibili_video_comment',
        'creator': 'bilibili_up_info'
    },
    'xiaohongshu': {
        'note': 'xhs_note',
        'comment': 'xhs_note_comment',
        'creator': 'xhs_creator'
    },
    'douyin': {
        'video': 'douyin_aweme',
        'comment': 'douyin_aweme_comment',
        'creator': 'dy_creator'
    },
    'kuaishou': {
        'video': 'kuaishou_video',
        'comment': 'kuaishou_video_comment',
        'creator': 'kuaishou_creator'
    },
    'weibo': {
        'note': 'weibo_note',
        'comment': 'weibo_note_comment',
        'creator': 'weibo_creator'
    },
    'tieba': {
        'note': 'tieba_note',
        'comment': 'tieba_comment',
        'creator': 'tieba_creator'
    },
    'zhihu': {
        'note': 'zhihu_content',
        'comment': 'zhihu_comment',
        'creator': 'zhihu_creator'
    }
}

async def get_table_count(db_conn, table_name):
    """获取表中的数据数量"""
    try:
        count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
        result = await db_conn.query(count_sql)
        return result[0]['count'] if result else 0
    except Exception as e:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️  获取表 {table_name} 数量失败: {e}")
        return 0

async def clear_table(db_conn, table_name):
    """清空指定表的数据"""
    try:
        # 获取清理前的数量
        before_count = await get_table_count(db_conn, table_name)
        
        if before_count == 0:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 📋 表 {table_name} 已为空，跳过")
            return True, 0
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🗑️  正在清空表 {table_name} ({before_count:,} 条数据)...")
        
        # 清空数据
        delete_sql = f"DELETE FROM {table_name}"
        affected_rows = await db_conn.execute(delete_sql)
        
        # 验证清理结果
        after_count = await get_table_count(db_conn, table_name)
        
        if after_count == 0:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 成功清空表 {table_name} ({affected_rows:,} 条数据)")
            return True, affected_rows
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️  表 {table_name} 仍有 {after_count} 条数据")
            return False, affected_rows
            
    except Exception as e:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 清空表 {table_name} 失败: {e}")
        return False, 0

async def clear_platform_data(db_conn, platform):
    """清空指定平台的所有数据"""
    if platform not in PLATFORM_TABLES:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 不支持的平台: {platform}")
        return False
    
    tables = PLATFORM_TABLES[platform]
    total_cleared = 0
    success_count = 0
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 开始清理 {platform.upper()} 平台数据...")
    
    # 按顺序清理：先清理评论，再清理内容，最后清理创作者
    table_order = ['comment', 'video', 'note', 'creator']
    
    for table_type in table_order:
        if table_type in tables:
            table_name = tables[table_type]
            success, cleared_count = await clear_table(db_conn, table_name)
            if success:
                success_count += 1
                total_cleared += cleared_count
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 {platform.upper()} 平台清理完成: 共清理 {total_cleared:,} 条数据")
    return success_count == len([t for t in table_order if t in tables])

async def clear_all_platforms_data(db_conn):
    """清空所有平台的数据"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🌐 开始清理所有平台数据...")
    
    total_cleared = 0
    success_platforms = 0
    
    for platform in PLATFORM_TABLES.keys():
        print(f"\n{'='*50}")
        success = await clear_platform_data(db_conn, platform)
        if success:
            success_platforms += 1
    
    print(f"\n{'='*50}")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 所有平台清理完成!")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 成功清理 {success_platforms}/{len(PLATFORM_TABLES)} 个平台")
    
    return success_platforms == len(PLATFORM_TABLES)

async def show_database_status(db_conn):
    """显示数据库状态"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 数据库状态统计:")
    print("-" * 60)
    
    total_records = 0
    
    for platform, tables in PLATFORM_TABLES.items():
        platform_total = 0
        print(f"\n🎯 {platform.upper()} 平台:")
        
        for table_type, table_name in tables.items():
            count = await get_table_count(db_conn, table_name)
            platform_total += count
            total_records += count
            
            if count > 0:
                print(f"  📋 {table_name}: {count:,} 条")
            else:
                print(f"  📋 {table_name}: 空")
        
        if platform_total > 0:
            print(f"  📊 平台小计: {platform_total:,} 条")
    
    print(f"\n{'='*60}")
    print(f"📊 数据库总计: {total_records:,} 条记录")
    print(f"{'='*60}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="数据库清理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 quick_clear_all_data.py --status                    # 查看数据库状态
  python3 quick_clear_all_data.py --platform bilibili        # 清理B站数据
  python3 quick_clear_all_data.py --all                       # 清理所有数据
  python3 quick_clear_all_data.py --platform xiaohongshu     # 清理小红书数据

支持的平台:
  bilibili, xiaohongshu, douyin, kuaishou, weibo, tieba, zhihu
        """
    )
    
    parser.add_argument(
        '--platform',
        type=str,
        choices=list(PLATFORM_TABLES.keys()),
        help='指定要清理的平台'
    )
    
    parser.add_argument(
        '--all',
        action='store_true',
        help='清理所有平台数据'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示数据库状态（不执行清理）'
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何参数，显示帮助
    if not any([args.platform, args.all, args.status]):
        parser.print_help()
        return 1
    
    print("🧹 数据库清理工具")
    print("=" * 50)
    
    pool = None
    
    try:
        # 初始化数据库连接
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 正在连接数据库...")
        pool = await aiomysql.create_pool(
            host=db_config.RELATION_DB_HOST,
            port=db_config.RELATION_DB_PORT,
            user=db_config.RELATION_DB_USER,
            password=db_config.RELATION_DB_PWD,
            db=db_config.RELATION_DB_NAME,
            autocommit=True,
        )
        db_conn = AsyncMysqlDB(pool)
        print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ 数据库连接成功")
        
        # 根据参数执行相应操作
        if args.status:
            # 显示数据库状态
            await show_database_status(db_conn)
            return 0
        
        elif args.all:
            # 清理所有平台数据
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️  即将清理所有平台数据!")
            success = await clear_all_platforms_data(db_conn)
            
            if success:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 所有数据清理完成")
                return 0
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 💥 部分数据清理失败")
                return 1
        
        elif args.platform:
            # 清理指定平台数据
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️  即将清理 {args.platform.upper()} 平台数据!")
            success = await clear_platform_data(db_conn, args.platform)
            
            if success:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎉 {args.platform.upper()} 平台数据清理完成")
                return 0
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 💥 {args.platform.upper()} 平台数据清理失败")
                return 1
        
    except Exception as e:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ 操作失败: {e}")
        return 1
    
    finally:
        if pool:
            pool.close()
            await pool.wait_closed()
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 数据库连接已关闭")

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 