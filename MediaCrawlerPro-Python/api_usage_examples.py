#!/usr/bin/env python3
"""
MediaCrawlerPro HTTP API 使用示例
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8080"

def create_bv_task(bv_json_file_path):
    """创建BV号批量爬取任务"""
    url = f"{BASE_URL}/tasks"
    
    payload = {
        "platform": "bili",
        "type": "detail", 
        "bv_complex_json_file": bv_json_file_path
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"🚀 创建任务...")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    
    response = requests.post(url, json=payload, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        task_id = result.get('task_id')
        print(f"✅ 任务创建成功!")
        print(f"任务ID: {task_id}")
        return task_id
    else:
        print(f"❌ 任务创建失败: {response.status_code}")
        print(f"错误信息: {response.text}")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    url = f"{BASE_URL}/tasks/{task_id}"
    
    response = requests.get(url)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ 获取任务状态失败: {response.status_code}")
        return None

def monitor_task(task_id, check_interval=5):
    """监控任务执行状态"""
    print(f"\n📊 开始监控任务: {task_id}")
    print(f"检查间隔: {check_interval}秒")
    
    while True:
        status_data = get_task_status(task_id)
        
        if not status_data:
            break
            
        status = status_data.get('status')
        progress = status_data.get('progress', {})
        
        print(f"\n⏰ {time.strftime('%H:%M:%S')} - 任务状态: {status}")
        
        if progress:
            total = progress.get('total', 0)
            processed = progress.get('processed', 0)
            success = progress.get('success', 0)
            failed = progress.get('failed', 0)
            
            if total > 0:
                percentage = (processed / total) * 100
                print(f"📈 进度: {processed}/{total} ({percentage:.1f}%)")
                print(f"✅ 成功: {success}, ❌ 失败: {failed}")
        
        if status in ['completed', 'failed', 'cancelled']:
            print(f"\n🏁 任务结束，最终状态: {status}")
            
            if status == 'failed':
                error = status_data.get('error')
                if error:
                    print(f"❌ 错误信息: {error}")
            
            break
            
        time.sleep(check_interval)

def list_all_tasks():
    """列出所有任务"""
    url = f"{BASE_URL}/tasks"
    
    response = requests.get(url)
    
    if response.status_code == 200:
        tasks = response.json()
        print(f"\n📋 共有 {len(tasks)} 个任务:")
        
        for task in tasks:
            task_id = task.get('id')
            status = task.get('status')
            created_at = task.get('created_at')
            config = task.get('config', {})
            
            print(f"\n任务ID: {task_id}")
            print(f"状态: {status}")
            print(f"创建时间: {created_at}")
            print(f"配置: {json.dumps(config, ensure_ascii=False)}")
    else:
        print(f"❌ 获取任务列表失败: {response.status_code}")

def main():
    """主函数 - 演示完整的API调用流程"""
    print("=" * 60)
    print("🎯 MediaCrawlerPro HTTP API 使用示例")
    print("=" * 60)
    
    # 1. 创建任务
    bv_file_path = "/Users/<USER>/Desktop/MediaCrawlerPro/bv.json"
    task_id = create_bv_task(bv_file_path)
    
    if not task_id:
        return
    
    # 2. 监控任务执行
    monitor_task(task_id, check_interval=3)
    
    # 3. 列出所有任务
    list_all_tasks()

if __name__ == "__main__":
    main()
