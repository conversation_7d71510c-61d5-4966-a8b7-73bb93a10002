#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装HTTP中间件依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"❌ {package} 未安装")
        return False

def main():
    """主函数"""
    print("🚀 MediaCrawlerPro HTTP中间件依赖安装")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        ("flask", "Flask"),
        ("requests", "requests")
    ]
    
    all_installed = True
    
    for import_name, package_name in packages:
        if not check_package(import_name):
            if not install_package(package_name):
                all_installed = False
    
    print("\n" + "=" * 50)
    if all_installed:
        print("🎉 所有依赖安装完成！")
        print("\n可以使用以下命令启动HTTP服务:")
        print("python3 start_http_server.py")
        print("\n或者:")
        print("python3 http_middleware.py")
    else:
        print("❌ 部分依赖安装失败，请手动安装")
        print("\n手动安装命令:")
        for import_name, package_name in packages:
            print(f"pip install {package_name}")

if __name__ == '__main__':
    main()
