# 声明：本代码仅供学习和研究目的使用。使用者应遵守以下原则：
# 1. 不得用于任何商业用途。
# 2. 使用时应遵守目标平台的使用条款和robots.txt规则。
# 3. 不得进行大规模爬取或对平台造成运营干扰。
# 4. 应合理控制请求频率，避免给目标平台带来不必要的负担。
# 5. 不得用于任何非法或不当的用途。
#
# 详细许可条款请参阅项目根目录下的LICENSE文件。
# 使用本代码即表示您同意遵守上述原则和LICENSE中的所有条款。


# -*- coding: utf-8 -*-
DOUYIN_INDEX_URL = "https://www.douyin.com"
DOUYIN_API_URL = DOUYIN_INDEX_URL
DOUYIN_FIXED_USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
DOUYIN_MS_TOKEN_REQ_URL = "https://mssdk.bytedance.com/web/common"
DOUYIN_MS_TOKEN_REQ_STR_DATA = "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"
DOUYIN_WEBID_REQ_URL = (
    "https://mcs.zijieapi.com/webid?aid=6383&sdk_version=5.1.18_zip&device_platform=web"
)


DOUYIN_VIDEO_TYPE = 0
DOUYIN_NOTE_TYPE = 68
