#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词自动爬虫程序
从指定文件中读取关键词列表，逐个进行爬取
支持多种文件格式：txt、json、csv
支持Windows、macOS、Linux跨平台运行
"""

import subprocess
import time
import datetime
import os
import sys
import signal
import logging
import shutil
import threading
import platform
import json
import csv
import argparse
from typing import List, Optional, Dict


class KeywordAutoCrawler:
    def __init__(self, keywords_file: str, platform: str = "bili", crawler_type: str = "search", 
                 wait_time: int = 300, max_notes: int = 100, resume: bool = True):
        # 检测操作系统
        import platform as platform_module
        self.is_windows = platform_module.system().lower() == 'windows'
        
        # Windows下设置环境变量解决编码问题
        if self.is_windows:
            self.setup_windows_encoding()
        
        # 配置日志
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        log_handlers = [
            logging.FileHandler('keyword_auto_crawler.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=log_handlers
        )
        self.logger = logging.getLogger(__name__)
        
        # 自动检测Python命令
        self.python_cmd = self.detect_python_command()
        
        # 参数设置
        self.keywords_file = keywords_file
        self.platform = platform
        self.crawler_type = crawler_type
        self.wait_time = wait_time  # 每个关键词之间的等待时间（秒）
        self.max_notes = max_notes  # 每个关键词爬取的最大数量
        self.resume = resume  # 是否启用断点续传
        
        # 运行标志
        self.running = True
        self.current_process = None
        
        # 关键词列表
        self.keywords = []
        
        # 进度保存相关
        self.progress_file = self.get_progress_filename()
        self.progress_data = {}
        
        # 注册信号处理器，用于优雅退出
        self.setup_signal_handlers()
    
    def setup_windows_encoding(self):
        """设置Windows编码环境"""
        try:
            # 设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul 2>&1')
            
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            
            # 尝试设置控制台输出编码
            if hasattr(sys.stdout, 'reconfigure'):
                try:
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
                except:
                    pass
        except Exception as e:
            print(f"设置Windows编码时出现警告: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器，兼容Windows和Unix系统"""
        try:
            signal.signal(signal.SIGINT, self.signal_handler)
            if not self.is_windows:
                # Windows不支持SIGTERM
                signal.signal(signal.SIGTERM, self.signal_handler)
        except Exception as e:
            self.logger.warning(f"设置信号处理器时出现警告: {e}")
    
    def get_progress_filename(self) -> str:
        """生成进度文件名"""
        # 根据关键词文件名和平台生成唯一的进度文件名
        base_name = os.path.splitext(os.path.basename(self.keywords_file))[0]
        return f"progress_{base_name}_{self.platform}_{self.crawler_type}.json"
    
    def load_progress(self) -> Dict:
        """加载进度数据"""
        if not self.resume or not os.path.exists(self.progress_file):
            return {
                'total_keywords': 0,
                'processed_keywords': [],
                'failed_keywords': [],
                'last_update': None,
                'start_time': None,
                'session_id': datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            }
        
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.logger.info(f"📂 加载进度文件: {self.progress_file}")
                self.logger.info(f"📊 上次进度: 已处理 {len(data.get('processed_keywords', []))} 个关键词")
                return data
        except Exception as e:
            self.logger.warning(f"⚠️  加载进度文件失败: {e}，将创建新的进度记录")
            return {
                'total_keywords': 0,
                'processed_keywords': [],
                'failed_keywords': [],
                'last_update': None,
                'start_time': None,
                'session_id': datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            }
    
    def save_progress(self):
        """保存进度数据"""
        try:
            self.progress_data['last_update'] = datetime.datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"❌ 保存进度文件失败: {e}")
    
    def update_progress(self, keyword: str, status: str, details: str = ""):
        """更新进度状态"""
        if status == 'success':
            if keyword not in self.progress_data['processed_keywords']:
                self.progress_data['processed_keywords'].append(keyword)
            # 从失败列表中移除（如果存在）
            if keyword in self.progress_data['failed_keywords']:
                self.progress_data['failed_keywords'].remove(keyword)
        elif status == 'failed':
            if keyword not in self.progress_data['failed_keywords']:
                self.progress_data['failed_keywords'].append(keyword)
        
        self.save_progress()
        
        # 记录详细日志
        processed_count = len(self.progress_data['processed_keywords'])
        failed_count = len(self.progress_data['failed_keywords'])
        total = self.progress_data['total_keywords']
        remaining = total - processed_count
        
        self.logger.info(f"📊 进度更新: 总数 {total}, 已完成 {processed_count}, 失败 {failed_count}, 剩余 {remaining}")
    
    def get_remaining_keywords(self) -> List[str]:
        """获取未处理的关键词列表"""
        if not self.resume:
            return self.keywords
        
        processed = set(self.progress_data.get('processed_keywords', []))
        remaining = [kw for kw in self.keywords if kw not in processed]
        
        if len(remaining) < len(self.keywords):
            self.logger.info(f"🔄 断点续传: 跳过已处理的 {len(self.keywords) - len(remaining)} 个关键词")
            self.logger.info(f"📝 剩余待处理: {len(remaining)} 个关键词")
        
        return remaining
    
    def show_progress_summary(self):
        """显示进度摘要"""
        processed = self.progress_data.get('processed_keywords', [])
        failed = self.progress_data.get('failed_keywords', [])
        total = self.progress_data.get('total_keywords', 0)
        
        self.logger.info("📊 " + "=" * 50)
        self.logger.info("📊 当前进度摘要:")
        self.logger.info(f"📊 总关键词数: {total}")
        self.logger.info(f"📊 已成功处理: {len(processed)}")
        self.logger.info(f"📊 处理失败: {len(failed)}")
        self.logger.info(f"📊 剩余待处理: {total - len(processed)}")
        
        if len(processed) > 0:
            success_rate = (len(processed) / total) * 100 if total > 0 else 0
            self.logger.info(f"📊 成功率: {success_rate:.1f}%")
        
        if len(failed) > 0:
            self.logger.info(f"📊 失败的关键词: {', '.join(failed[:5])}{'...' if len(failed) > 5 else ''}")
        
        self.logger.info("📊 " + "=" * 50)
    
    def detect_python_command(self):
        """自动检测可用的Python命令"""
        if self.is_windows:
            python_commands = ['python', 'python3', 'py', 'python.exe', 'python3.exe']
        else:
            python_commands = ['python3', 'python', 'python3.9', 'python3.8', 'python3.7']
        
        for cmd in python_commands:
            if shutil.which(cmd):
                try:
                    result = subprocess.run([cmd, '--version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.logger.info(f"检测到Python命令: {cmd} - {result.stdout.strip()}")
                        return cmd
                except Exception:
                    continue
        
        # 如果都找不到，根据系统选择默认命令
        default_cmd = 'python' if self.is_windows else 'python3'
        self.logger.warning(f"无法检测到Python命令，使用默认的{default_cmd}")
        return default_cmd
    
    def signal_handler(self, signum, frame):
        """处理退出信号"""
        self.logger.info(f"接收到退出信号 {signum}，正在停止程序...")
        self.running = False
        if self.current_process:
            self.logger.info("正在终止当前运行的爬虫进程...")
            try:
                if self.is_windows:
                    # Windows下使用terminate()
                    self.current_process.terminate()
                else:
                    # Unix系统下可以使用kill()
                    self.current_process.terminate()
                
                # 等待进程结束，最多等待5秒
                try:
                    self.current_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.logger.warning("进程未能在5秒内正常结束，强制终止...")
                    if self.is_windows:
                        self.current_process.kill()
                    else:
                        self.current_process.kill()
            except Exception as e:
                self.logger.error(f"终止进程时发生错误: {e}")
    
    def load_keywords(self) -> bool:
        """从文件中加载关键词列表"""
        if not os.path.exists(self.keywords_file):
            self.logger.error(f"关键词文件不存在: {self.keywords_file}")
            return False
        
        try:
            file_ext = self.keywords_file.lower()
            
            if file_ext.endswith('.txt'):
                # 读取txt文件，每行一个关键词
                with open(self.keywords_file, 'r', encoding='utf-8') as f:
                    self.keywords = [line.strip() for line in f if line.strip()]
            
            elif file_ext.endswith('.json'):
                # 读取json文件，应该是一个字符串数组
                with open(self.keywords_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        self.keywords = [str(item).strip() for item in data if str(item).strip()]
                    else:
                        self.logger.error("JSON文件格式不正确，应为字符串数组")
                        return False
            
            elif file_ext.endswith('.csv'):
                # 读取csv文件，假设第一列是关键词
                with open(self.keywords_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    self.keywords = []
                    for row in reader:
                        if row and row[0].strip():
                            self.keywords.append(row[0].strip())
            
            else:
                self.logger.error(f"不支持的文件格式: {self.keywords_file}")
                self.logger.info("支持的格式: .txt, .json, .csv")
                return False
            
            # 去重并过滤空关键词，保持原始顺序
            seen = set()
            unique_keywords = []
            for kw in self.keywords:
                if kw and kw not in seen:
                    seen.add(kw)
                    unique_keywords.append(kw)
            self.keywords = unique_keywords
            
            if not self.keywords:
                self.logger.error("未找到有效的关键词")
                return False
            
            self.logger.info(f"成功加载 {len(self.keywords)} 个关键词")
            self.logger.info(f"关键词列表: {', '.join(self.keywords[:10])}{'...' if len(self.keywords) > 10 else ''}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载关键词文件时发生错误: {str(e)}")
            return False
    
    def print_output_realtime(self, process):
        """实时打印进程输出"""
        try:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 处理编码问题，特别是Windows下的中文显示
                    try:
                        print(output.strip())
                        sys.stdout.flush()
                    except UnicodeEncodeError:
                        # 如果遇到编码问题，尝试用其他方式输出
                        try:
                            print(output.strip().encode('utf-8', errors='ignore').decode('utf-8'))
                            sys.stdout.flush()
                        except:
                            print("[输出包含无法显示的字符]")
                            sys.stdout.flush()
        except Exception as e:
            self.logger.error(f"实时输出时发生错误: {e}")
    
    def run_crawler_for_keyword(self, keyword: str) -> bool:
        """为指定关键词运行爬虫"""
        try:
            # 构建命令
            command = [
                self.python_cmd, "main.py",
                "--platform", self.platform,
                "--type", self.crawler_type,
                "--keywords", keyword,
                "--save_data_option", "db"  # 默认保存到数据库
            ]
            
            self.logger.info(f"开始爬取关键词: '{keyword}'")
            self.logger.info(f"运行命令: {' '.join(command)}")
            self.logger.info("=" * 60)
            start_time = datetime.datetime.now()
            
            # 设置进程创建参数
            popen_kwargs = {
                'stdout': subprocess.PIPE,
                'stderr': subprocess.STDOUT,
                'text': True,
                'encoding': 'utf-8',
                'bufsize': 1,
                'universal_newlines': True
            }
            
            # 设置环境变量，解决编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            popen_kwargs['env'] = env
            
            # Windows特殊处理
            if self.is_windows:
                popen_kwargs['creationflags'] = subprocess.CREATE_NEW_PROCESS_GROUP
                # Windows下可能需要处理编码问题
                popen_kwargs['encoding'] = 'utf-8'
                popen_kwargs['errors'] = 'ignore'
            
            # 运行命令，实时显示输出
            self.current_process = subprocess.Popen(command, **popen_kwargs)
            
            # 创建线程来实时打印输出
            output_thread = threading.Thread(target=self.print_output_realtime, args=(self.current_process,))
            output_thread.daemon = True
            output_thread.start()
            
            # 等待进程完成
            self.logger.info(f"⏳ 等待关键词 '{keyword}' 的爬取进程完成...")
            return_code = self.current_process.wait()
            
            # 等待输出线程完成
            output_thread.join(timeout=5)  # 增加等待时间确保输出完整
            
            end_time = datetime.datetime.now()
            duration = end_time - start_time
            
            self.logger.info("=" * 60)
            # 记录运行结果
            if return_code == 0:
                self.logger.info(f"✅ 关键词 '{keyword}' 爬取成功，耗时: {duration}")
                self.logger.info(f"🎯 关键词 '{keyword}' 的所有数据已保存到数据库")
                # 更新进度为成功
                self.update_progress(keyword, 'success', f"耗时: {duration}")
            else:
                self.logger.error(f"❌ 关键词 '{keyword}' 爬取失败，返回码: {return_code}，耗时: {duration}")
                # 更新进度为失败
                self.update_progress(keyword, 'failed', f"返回码: {return_code}, 耗时: {duration}")
            
            self.current_process = None
            
            # 确保进程完全结束
            time.sleep(1)  # 给系统一点时间完成清理
            
            return return_code == 0
            
        except Exception as e:
            self.logger.error(f"爬取关键词 '{keyword}' 时发生异常: {str(e)}")
            self.current_process = None
            return False
    
    def wait_between_keywords(self, keyword: str):
        """关键词间的等待处理"""
        if self.wait_time <= 0:
            self.logger.info(f"✅ 关键词 '{keyword}' 处理完成，立即开始下一个关键词...")
            return
            
        self.logger.info(f"⏰ 关键词 '{keyword}' 爬取完成，等待 {self.wait_time} 秒后继续...")
        
        remaining = self.wait_time
        last_shown = -1
        
        while remaining > 0 and self.running:
            # 每10秒显示一次，或者最后10秒每秒显示
            if (remaining % 10 == 0 and remaining != last_shown) or remaining <= 10:
                if remaining <= 10:
                    self.logger.info(f"⏳ 还需等待 {remaining} 秒...")
                else:
                    self.logger.info(f"⏳ 还需等待 {remaining} 秒...")
                last_shown = remaining
            
            time.sleep(1)
            remaining -= 1
        
        if self.running:
            self.logger.info("✨ 等待结束，准备处理下一个关键词...")
    
    def check_environment(self):
        """检查运行环境"""
        import platform as platform_module
        self.logger.info("🔍 正在检查运行环境...")
        self.logger.info(f"🖥️  操作系统: {platform_module.system()} {platform_module.release()}")
        
        # 检查main.py是否存在
        if not os.path.exists("main.py"):
            self.logger.error("❌ main.py 文件不存在，请确保在正确的目录中运行此脚本")
            return False
        else:
            self.logger.info("✅ main.py 文件存在")
        
        # 检查关键词文件是否存在
        if not os.path.exists(self.keywords_file):
            self.logger.error(f"❌ 关键词文件不存在: {self.keywords_file}")
            return False
        else:
            self.logger.info(f"✅ 关键词文件存在: {self.keywords_file}")
        
        # 检查Python环境
        try:
            result = subprocess.run([self.python_cmd, "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.logger.info(f"✅ Python版本: {result.stdout.strip()}")
            else:
                self.logger.error(f"❌ Python命令执行失败: {result.stderr}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 无法检查Python版本: {str(e)}")
            return False
        
        return True
    
    def run(self):
        """主运行循环"""
        self.logger.info("🚀 " + "=" * 50)
        self.logger.info("🚀 关键词自动爬虫程序启动")
        self.logger.info(f"📁 当前工作目录: {os.getcwd()}")
        self.logger.info(f"📄 关键词文件: {self.keywords_file}")
        self.logger.info(f"🎯 爬取平台: {self.platform}")
        self.logger.info(f"🔍 爬取类型: {self.crawler_type}")
        self.logger.info(f"⏱️  等待间隔: {self.wait_time} 秒")
        self.logger.info("🚀 " + "=" * 50)
        
        # 检查环境
        if not self.check_environment():
            self.logger.error("❌ 环境检查失败，程序退出")
            return
        
        # 加载关键词
        if not self.load_keywords():
            self.logger.error("❌ 加载关键词失败，程序退出")
            return
        
        # 加载或初始化进度数据
        self.progress_data = self.load_progress()
        
        # 初始化进度数据（如果是第一次运行）
        if self.progress_data['total_keywords'] == 0:
            self.progress_data['total_keywords'] = len(self.keywords)
            self.progress_data['start_time'] = datetime.datetime.now().isoformat()
            self.save_progress()
        
        # 获取剩余未处理的关键词
        remaining_keywords = self.get_remaining_keywords()
        
        # 显示当前进度
        self.show_progress_summary()
        
        if not remaining_keywords:
            self.logger.info("🎉 所有关键词已处理完成！")
            return
        
        success_count = len(self.progress_data.get('processed_keywords', []))
        fail_count = len(self.progress_data.get('failed_keywords', []))
        total_count = len(self.keywords)
        
        try:
            for i, keyword in enumerate(remaining_keywords, 1):
                if not self.running:
                    break
                
                # 计算在原始列表中的位置
                original_index = self.keywords.index(keyword) + 1
                
                self.logger.info(f"\n🔄 开始处理关键词 ({original_index}/{total_count}): '{keyword}' - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.logger.info(f"📊 当前统计: 成功 {success_count} 个, 失败 {fail_count} 个, 剩余 {len(remaining_keywords) - i + 1} 个")
                
                # 运行爬虫并等待完成
                self.logger.info(f"🚀 启动关键词 '{keyword}' 的爬取任务...")
                success = self.run_crawler_for_keyword(keyword)
                
                # 实时更新计数
                success_count = len(self.progress_data.get('processed_keywords', []))
                fail_count = len(self.progress_data.get('failed_keywords', []))
                
                if success:
                    self.logger.info(f"✅ 关键词 '{keyword}' 处理完成 (成功)")
                else:
                    self.logger.info(f"❌ 关键词 '{keyword}' 处理完成 (失败)")
                
                if not self.running:
                    break
                
                # 如果不是最后一个关键词，则等待
                if i < len(remaining_keywords):
                    self.wait_between_keywords(keyword)
                else:
                    self.logger.info(f"🎉 所有关键词处理完成！")
        
        except KeyboardInterrupt:
            self.logger.info("⚠️  接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"❌ 程序运行时发生异常: {str(e)}")
        finally:
            # 显示最终进度摘要
            self.show_progress_summary()
            
            # 如果所有关键词都已处理完成，显示完成信息
            processed_count = len(self.progress_data.get('processed_keywords', []))
            if processed_count == total_count:
                self.logger.info("🎉 " + "=" * 50)
                self.logger.info("🎉 所有关键词已全部处理完成！")
                self.logger.info(f"🎯 进度文件已保存: {self.progress_file}")
                self.logger.info("🎉 " + "=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="关键词自动爬虫程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 keyword_auto_crawler.py keywords.txt                           # 使用默认设置逐个爬取（支持断点续传）
  python3 keyword_auto_crawler.py keywords.json --platform xhs          # 逐个爬取小红书
  python3 keyword_auto_crawler.py keywords.csv --wait-time 600          # 每个关键词完成后等待10分钟
  python3 keyword_auto_crawler.py keywords.txt --wait-time 0            # 连续爬取，无等待时间
  python3 keyword_auto_crawler.py keywords.txt --no-resume              # 禁用断点续传，从头开始
  python3 keyword_auto_crawler.py keywords.txt --show-progress          # 查看当前进度状态
  python3 keyword_auto_crawler.py keywords.txt --reset-progress         # 重置进度记录

断点续传功能:
  程序会自动保存进度到 progress_<文件名>_<平台>_<类型>.json 文件
  重新运行时会自动跳过已成功处理的关键词
  支持查看进度状态和重置进度记录

支持的文件格式:
  .txt   - 文本文件，每行一个关键词
  .json  - JSON文件，字符串数组格式
  .csv   - CSV文件，第一列为关键词

支持的平台:
  bili   - B站 (默认)
  xhs    - 小红书
  dy     - 抖音
  ks     - 快手
  wb     - 微博
  tieba  - 贴吧
  zhihu  - 知乎
        """
    )
    
    parser.add_argument(
        'keywords_file',
        help='关键词文件路径'
    )
    
    parser.add_argument(
        '--platform',
        type=str,
        choices=['bili', 'xhs', 'dy', 'ks', 'wb', 'tieba', 'zhihu'],
        default='bili',
        help='爬取平台 (默认: bili)'
    )
    
    parser.add_argument(
        '--type',
        type=str,
        choices=['search', 'detail', 'creator', 'homefeed'],
        default='search',
        help='爬取类型 (默认: search)'
    )
    
    parser.add_argument(
        '--wait-time',
        type=int,
        default=300,
        help='每个关键词完成后的等待时间（秒，默认: 300，设为0则无等待）'
    )
    
    parser.add_argument(
        '--max-notes',
        type=int,
        default=100,
        help='每个关键词爬取的最大数量 (默认: 100)'
    )
    
    parser.add_argument(
        '--no-resume',
        action='store_true',
        help='禁用断点续传功能，从头开始处理所有关键词'
    )
    
    parser.add_argument(
        '--show-progress',
        action='store_true',
        help='仅显示当前进度状态，不执行爬取任务'
    )
    
    parser.add_argument(
        '--reset-progress',
        action='store_true',
        help='重置进度记录，从头开始处理所有关键词'
    )
    
    args = parser.parse_args()
    
    # 检测操作系统并显示相应提示
    import platform as platform_module
    system = platform_module.system()
    if system == "Windows":
        print("🖥️  Windows系统检测")
        print("💡 提示: 在Windows下按 Ctrl+C 停止程序")
        print("🔧 正在设置UTF-8编码环境...")
    elif system == "Darwin":
        print("🍎 macOS系统检测")
        print("💡 提示: 按 Ctrl+C 或 Command+C 停止程序")
    else:
        print("🐧 Linux系统检测")
        print("💡 提示: 按 Ctrl+C 停止程序")
    
    print("🤖 关键词自动爬虫程序")
    print(f"📄 关键词文件: {args.keywords_file}")
    print(f"🎯 爬取平台: {args.platform}")
    print(f"🔍 爬取类型: {args.type}")
    if args.wait_time > 0:
        print(f"⏱️  完成间隔: 每个关键词完成后等待 {args.wait_time} 秒")
    else:
        print("⏱️  完成间隔: 连续处理，无等待时间")
    print("🔄 工作模式: 逐个关键词完成后再处理下一个")
    if not args.no_resume:
        print("💾 断点续传: 已启用，支持中断后继续处理")
    else:
        print("🚫 断点续传: 已禁用，将从头开始处理")
    print("📝 运行日志将保存到 keyword_auto_crawler.log 文件")
    print("-" * 50)
    
    crawler = KeywordAutoCrawler(
        keywords_file=args.keywords_file,
        platform=args.platform,
        crawler_type=args.type,
        wait_time=args.wait_time,
        max_notes=args.max_notes,
        resume=not args.no_resume  # 如果指定了--no-resume则禁用断点续传
    )
    
    # 处理特殊命令
    if args.reset_progress:
        # 重置进度
        if os.path.exists(crawler.progress_file):
            os.remove(crawler.progress_file)
            print(f"✅ 已重置进度文件: {crawler.progress_file}")
        else:
            print("📝 进度文件不存在，无需重置")
        return
    
    if args.show_progress:
        # 仅显示进度
        if not crawler.load_keywords():
            print("❌ 加载关键词失败")
            return
        
        crawler.progress_data = crawler.load_progress()
        if crawler.progress_data['total_keywords'] > 0:
            crawler.show_progress_summary()
            
            # 显示详细的失败关键词（如果有）
            failed = crawler.progress_data.get('failed_keywords', [])
            if failed:
                print(f"\n❌ 失败的关键词详情:")
                for kw in failed:
                    print(f"  - {kw}")
        else:
            print("📝 暂无进度记录")
        return
    
    # 正常运行爬虫
    crawler.run()


if __name__ == "__main__":
    main() 