#!/bin/bash

# MediaCrawlerPro 国内优化构建脚本
# 使用国内镜像源加速Docker构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_blue() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker"
        exit 1
    fi
    log_info "Docker运行正常"
}

# 清理旧镜像
cleanup_images() {
    log_info "清理旧镜像..."
    docker-compose down 2>/dev/null || true
    
    # 删除旧镜像
    docker rmi mediacrawlerpro-python_app 2>/dev/null || true
    docker rmi mediacrawlerpro-python_signsrv 2>/dev/null || true
    
    log_info "清理完成"
}

# 构建镜像
build_images() {
    log_info "开始构建镜像（使用国内源）..."
    
    # 设置Docker构建参数，使用国内镜像
    export DOCKER_BUILDKIT=1
    
    log_blue "构建签名服务镜像..."
    docker-compose build --no-cache signsrv
    
    log_blue "构建主应用镜像..."
    docker-compose build --no-cache app
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建必要的文件
    touch crawler_progress.json
    
    # 启动服务
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 15
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "服务启动成功！"
        log_blue "HTTP中间件: http://localhost:8080"
        log_blue "健康检查: http://localhost:8080/health"
        log_blue "签名服务: http://localhost:8989"
        log_blue "MySQL: localhost:3307"
        log_blue "Redis: localhost:6378"
        
        echo ""
        log_blue "测试API:"
        echo "curl http://localhost:8080/health"
    else
        log_error "服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "服务日志 (最后10行):"
    docker-compose logs --tail=10
}

# 主函数
main() {
    case "${1:-build}" in
        build)
            check_docker
            cleanup_images
            build_images
            start_services
            show_status
            ;;
        start)
            check_docker
            start_services
            ;;
        status)
            show_status
            ;;
        clean)
            cleanup_images
            ;;
        *)
            echo "使用方法: $0 [build|start|status|clean]"
            echo ""
            echo "命令说明:"
            echo "  build  - 清理并重新构建所有镜像 (默认)"
            echo "  start  - 启动服务"
            echo "  status - 查看状态"
            echo "  clean  - 清理镜像"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
