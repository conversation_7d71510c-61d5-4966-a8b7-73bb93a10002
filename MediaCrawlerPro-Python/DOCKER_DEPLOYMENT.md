# MediaCrawlerPro Docker 部署指南

## 📋 概述

本指南将帮助您使用Docker在本地部署MediaCrawlerPro程序，包含HTTP中间件API服务。

## 🛠️ 系统要求

- Docker 20.10+
- Docker Compose 1.29+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

## 📦 服务架构

Docker部署包含以下服务：

| 服务名 | 端口 | 描述 |
|--------|------|------|
| app | 8080 | MediaCrawlerPro HTTP中间件 |
| db | 3307 | MySQL 8.0 数据库 |
| redis | 6378 | Redis 7.0 缓存 |
| signsrv | 8989 | 签名服务 |

## 🚀 快速开始

### 1. 检查Docker环境

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 确保Docker服务运行
docker info
```

### 2. 准备部署文件

确保以下文件存在：
- `docker-compose.yaml` - Docker编排配置
- `Dockerfile` - 应用镜像构建文件
- `requirements.txt` - Python依赖
- `bv.json` - BV号数据文件（在上级目录）

### 3. 一键部署

```bash
# 使用部署脚本（推荐）
./docker_deploy.sh start

# 或手动部署
docker-compose up -d --build
```

### 4. 验证部署

```bash
# 检查服务状态
./docker_deploy.sh status

# 或手动检查
docker-compose ps
```

## 🔧 详细部署步骤

### 步骤1：环境准备

```bash
# 进入项目目录
cd MediaCrawlerPro-Python

# 检查必要文件
ls -la docker-compose.yaml Dockerfile requirements.txt

# 检查BV数据文件
ls -la ../bv.json
```

### 步骤2：配置文件检查

确保 `bv.json` 文件格式正确：
```json
[
  {
    "data": {
      "items": [
        {
          "bvid": "BV1aRKHz2Ejd"
        }
      ]
    }
  }
]
```

### 步骤3：构建和启动

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看启动日志
docker-compose logs -f app
```

### 步骤4：服务验证

```bash
# 健康检查
curl http://localhost:8080/health

# 测试API
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{"platform": "bili", "type": "detail", "bv_complex_json_file": "/app/bv.json"}'
```

## 📊 服务管理

### 启动服务
```bash
./docker_deploy.sh start
```

### 停止服务
```bash
./docker_deploy.sh stop
```

### 重启服务
```bash
./docker_deploy.sh restart
```

### 查看日志
```bash
./docker_deploy.sh logs
```

### 查看状态
```bash
./docker_deploy.sh status
```

## 🔍 API 使用

### 创建爬取任务
```bash
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "bili",
    "type": "detail", 
    "bv_complex_json_file": "/app/bv.json"
  }'
```

### 查看任务状态
```bash
curl http://localhost:8080/tasks/{task_id}
```

### 列出所有任务
```bash
curl http://localhost:8080/tasks
```

## 📁 数据持久化

### 数据卷挂载
- `mysql_db_data` - MySQL数据持久化
- `redis_data` - Redis数据持久化
- `./bv.json:/app/bv.json` - BV数据文件
- `./crawler_progress.json:/app/crawler_progress.json` - 爬取进度

### 备份数据
```bash
# 备份MySQL数据
docker exec mysql_db mysqldump -u root -p123456 media_crawler > backup.sql

# 备份进度文件
cp crawler_progress.json crawler_progress_backup.json
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8080
   
   # 修改docker-compose.yaml中的端口映射
   ports:
     - "8081:8080"  # 改为其他端口
   ```

2. **内存不足**
   ```bash
   # 检查Docker资源限制
   docker system df
   docker system prune -f
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs app
   
   # 重新构建镜像
   docker-compose build --no-cache
   ```

### 日志查看
```bash
# 查看应用日志
docker-compose logs -f app

# 查看数据库日志
docker-compose logs -f db

# 查看所有服务日志
docker-compose logs -f
```

## 🔒 安全配置

### 修改默认密码
编辑 `docker-compose.yaml`：
```yaml
environment:
  - MYSQL_ROOT_PASSWORD: your_secure_password
  - REDIS_PASSWORD: your_redis_password
```

### 网络安全
```yaml
# 仅暴露必要端口
ports:
  - "127.0.0.1:8080:8080"  # 仅本地访问
```

## 📈 性能优化

### 资源限制
```yaml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### 缓存优化
```yaml
volumes:
  - ./cache:/app/cache  # 挂载缓存目录
```

## 🔄 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建和部署
./docker_deploy.sh restart
```

## 📞 技术支持

如遇问题，请检查：
1. Docker和Docker Compose版本
2. 系统资源使用情况
3. 网络连接状态
4. 日志文件内容

更多信息请参考项目文档或提交Issue。
