#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MediaCrawlerPro 数据库清空 API 服务
提供远程清空指定数据库表的HTTP接口
"""

from flask import Flask, jsonify, request
import mysql.connector
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_clear_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', '**********'),
    'port': int(os.getenv('DB_PORT', 3307)),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '123456'),
    'database': os.getenv('DB_NAME', 'media_crawler'),
    'charset': 'utf8mb4'
}

# API 安全密钥
API_SECRET_KEY = os.getenv('API_SECRET_KEY', 'mediacrawler_clear_2024')

# 允许清空的表列表
ALLOWED_TABLES = [
    'bilibili_video_comment',
    'bilibili_video', 
    'bilibili_up_info'
]

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except mysql.connector.Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def verify_api_key(request):
    """验证API密钥"""
    api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
    return api_key == API_SECRET_KEY

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'service': 'MediaCrawlerPro Database Clear API',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/tables/stats', methods=['GET'])
def get_table_stats():
    """获取表统计信息"""
    if not verify_api_key(request):
        return jsonify({'error': 'Invalid API key'}), 401
    
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        cursor = connection.cursor()
        
        # 构建统计查询
        stats_query = """
        SELECT 
            'bilibili_video_comment' as table_name, 
            COUNT(*) as record_count 
        FROM bilibili_video_comment
        UNION ALL
        SELECT 
            'bilibili_video' as table_name, 
            COUNT(*) as record_count 
        FROM bilibili_video
        UNION ALL
        SELECT 
            'bilibili_up_info' as table_name, 
            COUNT(*) as record_count 
        FROM bilibili_up_info
        ORDER BY table_name;
        """
        
        cursor.execute(stats_query)
        results = cursor.fetchall()
        
        stats = []
        total_records = 0
        for table_name, record_count in results:
            stats.append({
                'table_name': table_name,
                'record_count': record_count
            })
            total_records += record_count
        
        logger.info(f"获取表统计信息成功，总记录数: {total_records}")
        
        return jsonify({
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'total_records': total_records,
            'tables': stats
        })
        
    except mysql.connector.Error as e:
        logger.error(f"查询统计信息失败: {e}")
        return jsonify({'error': f'Database query failed: {str(e)}'}), 500
    
    finally:
        if connection:
            connection.close()

@app.route('/tables/clear', methods=['POST'])
def clear_tables():
    """清空指定表"""
    if not verify_api_key(request):
        return jsonify({'error': 'Invalid API key'}), 401
    
    # 获取请求参数
    data = request.get_json() or {}
    tables_to_clear = data.get('tables', ALLOWED_TABLES)
    reset_auto_increment = data.get('reset_auto_increment', True)
    
    # 验证表名
    invalid_tables = [table for table in tables_to_clear if table not in ALLOWED_TABLES]
    if invalid_tables:
        return jsonify({
            'error': f'Invalid table names: {invalid_tables}',
            'allowed_tables': ALLOWED_TABLES
        }), 400
    
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        cursor = connection.cursor()
        cleared_tables = []
        
        # 获取清空前的统计
        before_stats = {}
        for table in tables_to_clear:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            before_stats[table] = count
        
        # 清空表
        for table in tables_to_clear:
            # 删除数据
            cursor.execute(f"DELETE FROM {table}")
            affected_rows = cursor.rowcount
            
            # 重置自增ID
            if reset_auto_increment:
                cursor.execute(f"ALTER TABLE {table} AUTO_INCREMENT = 1")
            
            cleared_tables.append({
                'table_name': table,
                'records_deleted': affected_rows,
                'auto_increment_reset': reset_auto_increment
            })
            
            logger.info(f"清空表 {table}，删除 {affected_rows} 条记录")
        
        # 提交事务
        connection.commit()
        
        # 获取清空后的统计
        after_stats = {}
        for table in tables_to_clear:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            after_stats[table] = count
        
        total_deleted = sum(before_stats.values())
        
        logger.info(f"成功清空 {len(tables_to_clear)} 个表，总共删除 {total_deleted} 条记录")
        
        return jsonify({
            'status': 'success',
            'message': f'Successfully cleared {len(tables_to_clear)} tables',
            'timestamp': datetime.now().isoformat(),
            'total_records_deleted': total_deleted,
            'before_stats': before_stats,
            'after_stats': after_stats,
            'cleared_tables': cleared_tables
        })
        
    except mysql.connector.Error as e:
        connection.rollback()
        logger.error(f"清空表失败: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500
    
    finally:
        if connection:
            connection.close()

@app.route('/tables/clear/<table_name>', methods=['DELETE'])
def clear_single_table(table_name):
    """清空单个表"""
    if not verify_api_key(request):
        return jsonify({'error': 'Invalid API key'}), 401
    
    if table_name not in ALLOWED_TABLES:
        return jsonify({
            'error': f'Table {table_name} is not allowed',
            'allowed_tables': ALLOWED_TABLES
        }), 400
    
    reset_auto_increment = request.args.get('reset_auto_increment', 'true').lower() == 'true'
    
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'}), 500
    
    try:
        cursor = connection.cursor()
        
        # 获取清空前的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        before_count = cursor.fetchone()[0]
        
        # 清空表
        cursor.execute(f"DELETE FROM {table_name}")
        affected_rows = cursor.rowcount
        
        # 重置自增ID
        if reset_auto_increment:
            cursor.execute(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1")
        
        connection.commit()
        
        logger.info(f"清空表 {table_name}，删除 {affected_rows} 条记录")
        
        return jsonify({
            'status': 'success',
            'message': f'Successfully cleared table {table_name}',
            'timestamp': datetime.now().isoformat(),
            'table_name': table_name,
            'records_deleted': affected_rows,
            'before_count': before_count,
            'auto_increment_reset': reset_auto_increment
        })
        
    except mysql.connector.Error as e:
        connection.rollback()
        logger.error(f"清空表 {table_name} 失败: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500
    
    finally:
        if connection:
            connection.close()

@app.route('/tables', methods=['GET'])
def list_tables():
    """列出允许操作的表"""
    return jsonify({
        'status': 'success',
        'allowed_tables': ALLOWED_TABLES,
        'total_tables': len(ALLOWED_TABLES)
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    logger.info("启动 MediaCrawlerPro 数据库清空 API 服务")
    logger.info(f"允许清空的表: {ALLOWED_TABLES}")
    logger.info(f"数据库配置: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    
    # 开发环境
    app.run(host='0.0.0.0', port=8081, debug=False)
