# MediaCrawlerPro 数据库清空 API 使用说明

## 🚀 服务信息

- **服务地址**: http://1.15.151.6:8081
- **API密钥**: `mediacrawler_clear_2024`
- **支持的表**: `bilibili_video_comment`, `bilibili_video`, `bilibili_up_info`

## 📋 API 接口列表

### 1. 健康检查
```bash
GET /health
```
**功能**: 检查API服务状态  
**认证**: 无需认证  
**示例**:
```bash
curl http://1.15.151.6:8081/health
```
**响应**:
```json
{
  "service": "MediaCrawlerPro Database Clear API",
  "status": "ok",
  "timestamp": "2025-07-08T14:23:43.793799"
}
```

### 2. 获取表统计信息
```bash
GET /tables/stats
```
**功能**: 查看指定表的记录数统计  
**认证**: 需要API密钥  
**示例**:
```bash
curl -H "X-API-Key: mediacrawler_clear_2024" http://1.15.151.6:8081/tables/stats
```
**响应**:
```json
{
  "status": "success",
  "tables": [
    {"record_count": 0, "table_name": "bilibili_up_info"},
    {"record_count": 45, "table_name": "bilibili_video"},
    {"record_count": 4105, "table_name": "bilibili_video_comment"}
  ],
  "timestamp": "2025-07-08T14:23:54.762215",
  "total_records": 4150
}
```

### 3. 清空所有表
```bash
POST /tables/clear
```
**功能**: 清空所有支持的表  
**认证**: 需要API密钥  
**参数**:
- `tables` (可选): 指定要清空的表列表，默认清空所有表
- `reset_auto_increment` (可选): 是否重置自增ID，默认true

**示例1 - 清空所有表**:
```bash
curl -X POST \
  -H "X-API-Key: mediacrawler_clear_2024" \
  -H "Content-Type: application/json" \
  http://1.15.151.6:8081/tables/clear
```

**示例2 - 清空指定表**:
```bash
curl -X POST \
  -H "X-API-Key: mediacrawler_clear_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "tables": ["bilibili_video_comment", "bilibili_video"],
    "reset_auto_increment": true
  }' \
  http://1.15.151.6:8081/tables/clear
```

**响应**:
```json
{
  "status": "success",
  "message": "Successfully cleared 3 tables",
  "timestamp": "2025-07-08T14:25:00.000000",
  "total_records_deleted": 4150,
  "before_stats": {
    "bilibili_up_info": 0,
    "bilibili_video": 45,
    "bilibili_video_comment": 4105
  },
  "after_stats": {
    "bilibili_up_info": 0,
    "bilibili_video": 0,
    "bilibili_video_comment": 0
  },
  "cleared_tables": [
    {
      "table_name": "bilibili_video_comment",
      "records_deleted": 4105,
      "auto_increment_reset": true
    },
    {
      "table_name": "bilibili_video",
      "records_deleted": 45,
      "auto_increment_reset": true
    },
    {
      "table_name": "bilibili_up_info",
      "records_deleted": 0,
      "auto_increment_reset": true
    }
  ]
}
```

### 4. 清空单个表
```bash
DELETE /tables/clear/<table_name>
```
**功能**: 清空指定的单个表  
**认证**: 需要API密钥  
**参数**:
- `reset_auto_increment` (可选): 是否重置自增ID，默认true

**示例**:
```bash
# 清空评论表
curl -X DELETE \
  -H "X-API-Key: mediacrawler_clear_2024" \
  http://1.15.151.6:8081/tables/clear/bilibili_video_comment

# 清空视频表且不重置自增ID
curl -X DELETE \
  -H "X-API-Key: mediacrawler_clear_2024" \
  "http://1.15.151.6:8081/tables/clear/bilibili_video?reset_auto_increment=false"
```

**响应**:
```json
{
  "status": "success",
  "message": "Successfully cleared table bilibili_video_comment",
  "timestamp": "2025-07-08T14:26:00.000000",
  "table_name": "bilibili_video_comment",
  "records_deleted": 4105,
  "before_count": 4105,
  "auto_increment_reset": true
}
```

### 5. 列出支持的表
```bash
GET /tables
```
**功能**: 获取所有支持操作的表列表  
**认证**: 无需认证  
**示例**:
```bash
curl http://1.15.151.6:8081/tables
```
**响应**:
```json
{
  "status": "success",
  "allowed_tables": [
    "bilibili_video_comment",
    "bilibili_video",
    "bilibili_up_info"
  ],
  "total_tables": 3
}
```

## 🔐 认证方式

API使用Header认证，有两种方式提供API密钥：

1. **Header方式** (推荐):
```bash
-H "X-API-Key: mediacrawler_clear_2024"
```

2. **URL参数方式**:
```bash
?api_key=mediacrawler_clear_2024
```

## ⚠️ 安全注意事项

1. **API密钥保护**: 请妥善保管API密钥，不要在公开场所暴露
2. **操作不可逆**: 数据清空操作不可逆，请谨慎使用
3. **生产环境**: 建议在生产环境中使用更复杂的API密钥
4. **访问控制**: 建议配置防火墙限制API访问来源

## 🛠️ 服务管理

### 查看服务状态
```bash
sudo systemctl status mediacrawler-clear-api
```

### 重启服务
```bash
sudo systemctl restart mediacrawler-clear-api
```

### 查看服务日志
```bash
sudo journalctl -u mediacrawler-clear-api -f
```

### 停止服务
```bash
sudo systemctl stop mediacrawler-clear-api
```

## 📊 使用场景

1. **测试环境清理**: 在测试完成后清空测试数据
2. **定期维护**: 定期清理过期或无用数据
3. **重新开始**: 重置数据库状态，重新开始爬取
4. **故障恢复**: 在数据异常时快速清理问题数据

## 🔧 故障排除

### 1. 连接超时
- 检查服务器防火墙设置
- 确认端口8081是否开放
- 检查服务是否正在运行

### 2. 认证失败
- 确认API密钥是否正确
- 检查Header格式是否正确

### 3. 数据库连接失败
- 检查数据库服务是否运行
- 确认数据库连接参数是否正确
- 查看服务日志获取详细错误信息

---

**最后更新**: 2025-07-08  
**服务版本**: 1.0.0  
**维护者**: MediaCrawlerPro Team
