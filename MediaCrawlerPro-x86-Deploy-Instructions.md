# MediaCrawlerPro x86架构部署包

## 📦 部署包信息
- **文件名**: `MediaCrawlerPro-x86-Deploy-Final.tar.gz`
- **大小**: 908MB
- **架构**: x86_64 (amd64)
- **适用系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+

## 🐳 包含的Docker镜像
- `mediacrawlerpro:x86` (1.1GB) - 主应用程序
- `signsrv:x86` (1.0GB) - 签名服务
- `mysql:8.0` (753MB) - MySQL数据库 (x86架构)
- `redis:7.0` (107MB) - Redis缓存 (x86架构)

## 📋 部署文件清单
```
x86/
├── docker-compose.yaml         # Docker编排配置
├── load-images.sh             # 镜像加载脚本
├── deploy.sh                  # 一键部署脚本
├── README.md                  # 详细说明文档
├── QUICK_START.md             # 快速开始指南
├── config/                    # 应用配置目录
├── schema/                    # 数据库初始化脚本
├── mediacrawlerpro-x86.tar    # 主应用镜像
├── signsrv-x86.tar           # 签名服务镜像
├── mysql-8.0-x86.tar         # MySQL镜像
└── redis-7.0-x86.tar         # Redis镜像
```

## 🚀 部署步骤

### 1. 上传部署包
```bash
scp MediaCrawlerPro-x86-Deploy-Final.tar.gz user@server:/tmp/
```

### 2. 解压部署包
```bash
cd /opt  # 或其他部署目录
sudo tar -xzf /tmp/MediaCrawlerPro-x86-Deploy-Final.tar.gz
cd x86
```

### 3. 加载Docker镜像
```bash
sudo ./load-images.sh
```

### 4. 启动服务
```bash
sudo ./deploy.sh
```

## 🌐 服务端口
- **HTTP API**: 8080 (对外开放)
- **MySQL**: 3307 (可选择性开放)
- **Redis**: 6379 (内部服务)
- **签名服务**: 8989 (内部服务)

## 🔧 环境要求
- Docker 20.10+
- Docker Compose 1.29+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

## ✅ 验证部署
```bash
# 检查容器状态
sudo docker ps

# 测试HTTP API
curl http://localhost:8080/health
```

## 📝 注意事项
1. 所有镜像均为x86_64架构，确保服务器兼容性
2. 首次启动可能需要1-2分钟初始化数据库
3. 确保防火墙开放8080端口用于API访问
4. 建议在独立磁盘上部署以获得更好性能

## 🆘 故障排除
- 如果容器启动失败，检查日志: `sudo docker logs <container_name>`
- 如果端口冲突，修改docker-compose.yaml中的端口映射
- 如果内存不足，可以调整MySQL配置或增加服务器内存
